'use client';

import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, Button, Tag, Divider } from 'antd';
import { useAuth } from '@/components/AuthProvider';

const { Text, Paragraph } = Typography;

const AuthDebugger: React.FC = () => {
  const { user, loading } = useAuth();
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [localStorageToken, setLocalStorageToken] = useState<string | null>(null);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    // 检查localStorage中的token
    const token = localStorage.getItem('token');
    setLocalStorageToken(token);

    if (token) {
      try {
        // 解析JWT token（仅用于调试）
        const payload = JSON.parse(atob(token.split('.')[1]));
        setTokenInfo(payload);
      } catch (error) {
        console.error('解析token失败:', error);
      }
    }
  }, [user]);

  const clearToken = () => {
    localStorage.removeItem('token');
    setLocalStorageToken(null);
    setTokenInfo(null);
    window.location.reload();
  };

  const testApiCall = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      console.log('API测试结果:', data);
      alert(`API测试结果: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error('API测试失败:', error);
      alert(`API测试失败: ${error}`);
    }
  };

  return (
    <Card 
      title="认证调试信息" 
      size="small"
      style={{ 
        position: 'fixed', 
        bottom: '20px', 
        right: '20px', 
        width: '300px',
        zIndex: 1000,
        fontSize: '12px'
      }}
    >
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <div>
          <Text strong>加载状态: </Text>
          <Tag color={loading ? 'orange' : 'green'}>
            {loading ? '加载中' : '已完成'}
          </Tag>
        </div>

        <div>
          <Text strong>用户状态: </Text>
          <Tag color={user ? 'green' : 'red'}>
            {user ? '已登录' : '未登录'}
          </Tag>
        </div>

        {user && (
          <div>
            <Text strong>用户信息: </Text>
            <Paragraph style={{ fontSize: '11px', margin: 0 }}>
              ID: {user.id}<br/>
              邮箱: {user.email}<br/>
              角色: {user.role}
            </Paragraph>
          </div>
        )}

        <div>
          <Text strong>本地Token: </Text>
          <Tag color={localStorageToken ? 'green' : 'red'}>
            {localStorageToken ? '存在' : '不存在'}
          </Tag>
        </div>

        {tokenInfo && (
          <div>
            <Text strong>Token信息: </Text>
            <Paragraph style={{ fontSize: '11px', margin: 0 }}>
              用户ID: {tokenInfo.userId}<br/>
              角色: {tokenInfo.role}<br/>
              过期时间: {new Date(tokenInfo.exp * 1000).toLocaleString()}
            </Paragraph>
          </div>
        )}

        <Divider style={{ margin: '8px 0' }} />

        <Space size="small">
          <Button size="small" onClick={testApiCall}>
            测试API
          </Button>
          <Button size="small" danger onClick={clearToken}>
            清除Token
          </Button>
        </Space>
      </Space>
    </Card>
  );
};

export default AuthDebugger;
