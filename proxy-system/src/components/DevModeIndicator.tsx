'use client';

import React, { useEffect } from 'react';
import { Tag, Space } from 'antd';
import { ExperimentOutlined, InfoCircleOutlined } from '@ant-design/icons';

const DevModeIndicator: React.FC = () => {
  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // 添加body类以调整页面布局
  useEffect(() => {
    document.body.classList.add('dev-mode');
    return () => {
      document.body.classList.remove('dev-mode');
    };
  }, []);

  return (
    <div className="dev-mode-indicator">
      <Space size="middle">
        <Space size="small">
          <ExperimentOutlined />
          <span>开发模式</span>
        </Space>

        <Tag color="orange" style={{ margin: 0 }}>
          演示版本
        </Tag>

        <Space size="small">
          <InfoCircleOutlined />
          <span>默认管理员: <EMAIL> / admin123456</span>
        </Space>

        <span>|</span>

        <span>数据存储在本地文件中</span>
      </Space>
    </div>
  );
};

export default DevModeIndicator;
