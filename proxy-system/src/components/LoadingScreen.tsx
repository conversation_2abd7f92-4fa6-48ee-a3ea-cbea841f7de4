'use client';

import React, { useState, useEffect } from 'react';
import { Typography } from 'antd';
import { Globe, Wifi, Shield, Zap } from 'lucide-react';

const { Text } = Typography;

interface LoadingScreenProps {
  message?: string;
}

export default function LoadingScreen({ message = "系统初始化中..." }: LoadingScreenProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    { icon: Globe, text: "连接全球网络", color: "from-blue-500 to-indigo-600" },
    { icon: Shield, text: "安全验证中", color: "from-green-500 to-emerald-600" },
    { icon: Wifi, text: "优化连接", color: "from-purple-500 to-pink-600" },
    { icon: Zap, text: "准备就绪", color: "from-orange-500 to-red-600" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          return 100;
        }
        const newProgress = prev + Math.random() * 15;
        const stepIndex = Math.floor((newProgress / 100) * steps.length);
        setCurrentStep(Math.min(stepIndex, steps.length - 1));
        return Math.min(newProgress, 100);
      });
    }, 300);

    return () => clearInterval(interval);
  }, []);

  const CurrentIcon = steps[currentStep].icon;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* 背景动画元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-indigo-200 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className="text-center relative z-10">
        {/* 主Logo动画 */}
        <div className="mb-8">
          <div className="relative">
            {/* 外圈旋转动画 */}
            <div className="absolute inset-0 w-24 h-24 mx-auto">
              <div className="w-full h-full border-4 border-transparent border-t-blue-500 border-r-indigo-500 rounded-full animate-spin"></div>
            </div>

            {/* 中圈反向旋转 */}
            <div className="absolute inset-2 w-20 h-20 mx-auto">
              <div className="w-full h-full border-3 border-transparent border-b-purple-400 border-l-pink-400 rounded-full animate-spin-reverse"></div>
            </div>

            {/* 中心Logo */}
            <div className={`w-24 h-24 bg-gradient-to-r ${steps[currentStep].color} rounded-2xl flex items-center justify-center mx-auto shadow-2xl transform transition-all duration-500`}>
              <CurrentIcon className="text-white animate-pulse" size={40} />
            </div>
          </div>
        </div>

        {/* 品牌名称 */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
            ProxyHub
          </h1>
          <Text className="text-gray-600 text-lg">专业的代理服务平台</Text>
        </div>

        {/* 当前步骤显示 */}
        <div className="mb-6">
          <div className="flex justify-center items-center space-x-2 mb-4">
            <CurrentIcon className="text-blue-600" size={20} />
            <Text className="text-gray-700 text-lg font-medium">
              {steps[currentStep].text}
            </Text>
          </div>
          <Text className="text-gray-500">{message}</Text>
        </div>

        {/* 进度条 */}
        <div className="mb-8 w-80 mx-auto">
          <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
            <div
              className={`bg-gradient-to-r ${steps[currentStep].color} h-3 rounded-full transition-all duration-300 ease-out shadow-lg`}
              style={{ width: `${progress}%` }}
            >
              <div className="h-full bg-white bg-opacity-30 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="flex justify-between mt-2 text-sm text-gray-500">
            <span>0%</span>
            <span className="font-medium">{Math.round(progress)}%</span>
            <span>100%</span>
          </div>
        </div>

        {/* 步骤指示器 */}
        <div className="flex justify-center space-x-4 mb-8">
          {steps.map((step, index) => {
            const StepIcon = step.icon;
            return (
              <div
                key={index}
                className={`flex flex-col items-center transition-all duration-300 ${
                  index <= currentStep ? 'opacity-100' : 'opacity-40'
                }`}
              >
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                    index === currentStep
                      ? `bg-gradient-to-r ${step.color} shadow-lg scale-110`
                      : index < currentStep
                      ? 'bg-green-500'
                      : 'bg-gray-300'
                  }`}
                >
                  <StepIcon
                    className={`${index <= currentStep ? 'text-white' : 'text-gray-500'}`}
                    size={20}
                  />
                </div>
                <Text className={`text-xs ${index <= currentStep ? 'text-gray-700' : 'text-gray-400'}`}>
                  {step.text}
                </Text>
              </div>
            );
          })}
        </div>

        {/* 底部提示 */}
        <div className="max-w-md mx-auto">
          <Text className="text-gray-500 text-sm leading-relaxed">
            正在为您准备最佳的代理服务体验，请稍候...
          </Text>
        </div>
      </div>
    </div>
  );
}
