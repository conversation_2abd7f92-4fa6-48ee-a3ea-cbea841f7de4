'use client';

import React from 'react';
import { Spin, Typography } from 'antd';
import { Globe } from 'lucide-react';

const { Text } = Typography;

interface LoadingScreenProps {
  message?: string;
}

export default function LoadingScreen({ message = "系统初始化中..." }: LoadingScreenProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        {/* Logo动画 */}
        <div className="mb-8">
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto animate-pulse">
              <Globe className="text-white" size={36} />
            </div>
            {/* 环形加载动画 */}
            <div className="absolute inset-0 w-20 h-20 mx-auto">
              <div className="w-full h-full border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
        
        {/* 品牌名称 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">ProxyHub</h1>
          <Text className="text-gray-600">专业的代理服务平台</Text>
        </div>
        
        {/* 加载指示器 */}
        <div className="mb-4">
          <Spin size="large" />
        </div>
        
        {/* 加载消息 */}
        <Text className="text-gray-600 text-lg">{message}</Text>
        
        {/* 加载进度条动画 */}
        <div className="mt-6 w-64 mx-auto">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>
        </div>
        
        {/* 提示文字 */}
        <div className="mt-8 max-w-md mx-auto">
          <Text className="text-gray-500 text-sm">
            正在为您准备最佳的代理服务体验...
          </Text>
        </div>
      </div>
    </div>
  );
}
