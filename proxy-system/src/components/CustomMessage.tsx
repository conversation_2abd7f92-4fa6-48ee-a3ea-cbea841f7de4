'use client';

import React, { useState, useEffect } from 'react';

interface MessageItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'loading';
  content: string;
  duration?: number;
}

interface CustomMessageProps {
  messages: MessageItem[];
  onRemove: (id: string) => void;
}

const CustomMessage: React.FC<CustomMessageProps> = ({ messages, onRemove }) => {
  useEffect(() => {
    messages.forEach(message => {
      if (message.duration && message.duration > 0) {
        const timer = setTimeout(() => {
          onRemove(message.id);
        }, message.duration * 1000);

        return () => clearTimeout(timer);
      }
    });
  }, [messages, onRemove]);

  const getMessageStyle = (type: string) => {
    const baseStyle = {
      position: 'fixed' as const,
      top: '100px',
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: 9999,
      padding: '12px 24px',
      borderRadius: '6px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      fontSize: '14px',
      fontWeight: '500',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      minWidth: '300px',
      maxWidth: '500px',
      animation: 'slideInDown 0.3s ease-out',
    };

    const typeStyles = {
      success: {
        backgroundColor: '#f6ffed',
        border: '1px solid #b7eb8f',
        color: '#52c41a',
      },
      error: {
        backgroundColor: '#fff2f0',
        border: '1px solid #ffccc7',
        color: '#ff4d4f',
      },
      warning: {
        backgroundColor: '#fffbe6',
        border: '1px solid #ffe58f',
        color: '#faad14',
      },
      info: {
        backgroundColor: '#e6f7ff',
        border: '1px solid #91d5ff',
        color: '#1890ff',
      },
      loading: {
        backgroundColor: '#f0f0f0',
        border: '1px solid #d9d9d9',
        color: '#666',
      },
    };

    return { ...baseStyle, ...typeStyles[type as keyof typeof typeStyles] };
  };

  const getIcon = (type: string) => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      loading: '🔄',
    };
    return icons[type as keyof typeof icons] || '';
  };

  if (messages.length === 0) return null;

  return (
    <>
      <style jsx global>{`
        @keyframes slideInDown {
          from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
        }
      `}</style>
      
      <div>
        {messages.map((message, index) => (
          <div
            key={message.id}
            style={{
              ...getMessageStyle(message.type),
              top: `${100 + index * 60}px`,
            }}
          >
            <span>{getIcon(message.type)}</span>
            <span>{message.content}</span>
            <button
              onClick={() => onRemove(message.id)}
              style={{
                marginLeft: 'auto',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '16px',
                opacity: 0.6,
              }}
            >
              ×
            </button>
          </div>
        ))}
      </div>
    </>
  );
};

// 全局消息管理器
class MessageManager {
  private messages: MessageItem[] = [];
  private listeners: ((messages: MessageItem[]) => void)[] = [];

  subscribe(listener: (messages: MessageItem[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notify() {
    this.listeners.forEach(listener => listener([...this.messages]));
  }

  private add(type: MessageItem['type'], content: string, duration = 3) {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const message: MessageItem = { id, type, content, duration };
    
    this.messages.push(message);
    this.notify();

    if (duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, duration * 1000);
    }

    return () => this.remove(id);
  }

  remove(id: string) {
    this.messages = this.messages.filter(m => m.id !== id);
    this.notify();
  }

  success(content: string, duration = 3) {
    return this.add('success', content, duration);
  }

  error(content: string, duration = 4) {
    return this.add('error', content, duration);
  }

  warning(content: string, duration = 3) {
    return this.add('warning', content, duration);
  }

  info(content: string, duration = 3) {
    return this.add('info', content, duration);
  }

  loading(content: string) {
    return this.add('loading', content, 0); // 不自动关闭
  }
}

export const customMessage = new MessageManager();

// React Hook
export const useCustomMessage = () => {
  const [messages, setMessages] = useState<MessageItem[]>([]);

  useEffect(() => {
    const unsubscribe = customMessage.subscribe(setMessages);
    return unsubscribe;
  }, []);

  const handleRemove = (id: string) => {
    customMessage.remove(id);
  };

  return { messages, onRemove: handleRemove };
};

export default CustomMessage;
