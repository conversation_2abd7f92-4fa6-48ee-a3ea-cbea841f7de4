'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, ApiResponse } from '@/types';
import axios from 'axios';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取用户信息
  const fetchUser = async () => {
    try {
      // 检查本地存储的token
      const token = localStorage.getItem('token');
      if (!token) {
        setLoading(false);
        return;
      }

      // 设置axios默认header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      const response = await axios.get<ApiResponse<User>>('/api/auth/me');
      if (response.data.success && response.data.data) {
        setUser(response.data.data);
      } else {
        // token无效，清除本地存储
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
        setUser(null);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 清除无效token
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // 登录
  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post<ApiResponse<{ user: User; token: string }>>('/api/auth/login', {
        email,
        password
      });

      if (response.data.success && response.data.data) {
        const { user, token } = response.data.data;

        // 存储token到localStorage
        localStorage.setItem('token', token);

        // 设置axios默认header
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        setUser(user);
      } else {
        throw new Error(response.data.error || '登录失败');
      }
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw error;
    }
  };

  // 注册
  const register = async (email: string, username: string, password: string) => {
    try {
      const response = await axios.post<ApiResponse<{ user: User; token: string }>>('/api/auth/register', {
        email,
        username,
        password
      });

      if (response.data.success && response.data.data) {
        const { user, token } = response.data.data;

        // 存储token到localStorage
        localStorage.setItem('token', token);

        // 设置axios默认header
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        setUser(user);
      } else {
        throw new Error(response.data.error || '注册失败');
      }
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw error;
    }
  };

  // 退出登录
  const logout = async () => {
    console.log('🔍 AuthProvider: logout函数被调用');

    try {
      console.log('🔍 AuthProvider: 发送退出登录请求到API');
      await axios.post('/api/auth/logout');
      console.log('✅ AuthProvider: 退出登录API调用成功');
    } catch (error) {
      console.error('❌ AuthProvider: 退出登录API调用失败:', error);
    } finally {
      console.log('🔍 AuthProvider: 清除本地数据');

      // 清除本地存储的token
      localStorage.removeItem('token');
      console.log('🔍 AuthProvider: localStorage token已清除');

      // 清除axios默认header
      delete axios.defaults.headers.common['Authorization'];
      console.log('🔍 AuthProvider: axios Authorization header已清除');

      setUser(null);
      console.log('🔍 AuthProvider: 用户状态已清除');
      console.log('✅ AuthProvider: logout函数执行完成');
    }
  };

  // 刷新用户信息
  const refreshUser = async () => {
    await fetchUser();
  };

  // 检查是否为管理员
  const isAdmin = user?.role === 'admin';

  // 初始化时获取用户信息
  useEffect(() => {
    fetchUser();
  }, []);

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    refreshUser,
    isAdmin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
