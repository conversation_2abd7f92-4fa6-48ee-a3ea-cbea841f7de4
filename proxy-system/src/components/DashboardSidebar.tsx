'use client';

import React, { useState } from 'react';
import { Layout, Menu, Typography, Divider } from 'antd';
import {
  DashboardOutlined,
  GlobalOutlined,
  ShoppingCartOutlined,
  WalletOutlined,
  BarChartOutlined,
  SettingOutlined,
  UserOutlined,
  HistoryOutlined,
  TeamOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { Globe } from 'lucide-react';
import type { MenuProps } from 'antd';

const { Sider } = Layout;
const { Title } = Typography;

interface DashboardSidebarProps {
  isAdmin: boolean;
  collapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
}

type MenuItem = Required<MenuProps>['items'][number];

export default function DashboardSidebar({ isAdmin, collapsed = false, onCollapse }: DashboardSidebarProps) {
  const [selectedKey, setSelectedKey] = useState('dashboard');

  const userMenuItems: MenuItem[] = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '概览',
    },
    {
      key: 'proxies',
      icon: <GlobalOutlined />,
      label: '我的代理',
    },
    {
      key: 'purchase',
      icon: <ShoppingCartOutlined />,
      label: '购买代理',
    },
    {
      key: 'wallet',
      icon: <WalletOutlined />,
      label: '账户充值',
    },
    {
      key: 'statistics',
      icon: <BarChartOutlined />,
      label: '使用统计',
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: '使用记录',
    },
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人设置',
    },
  ];

  const adminMenuItems: MenuItem[] = [
    {
      key: 'admin-dashboard',
      icon: <DashboardOutlined />,
      label: '管理概览',
    },
    {
      key: 'user-management',
      icon: <TeamOutlined />,
      label: '用户管理',
    },
    {
      key: 'proxy-management',
      icon: <DatabaseOutlined />,
      label: '代理管理',
    },
    {
      key: 'system-settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setSelectedKey(e.key);

    // 路由跳转逻辑
    switch (e.key) {
      case 'dashboard':
        window.location.href = '/dashboard';
        break;
      case 'proxies':
        window.location.href = '/dashboard/proxies';
        break;
      case 'purchase':
        window.location.href = '/dashboard/purchase';
        break;
      case 'wallet':
        window.location.href = '/dashboard/wallet';
        break;
      case 'statistics':
        window.location.href = '/dashboard/statistics';
        break;
      case 'history':
        window.location.href = '/dashboard/history';
        break;
      case 'profile':
        window.location.href = '/dashboard/profile';
        break;
      case 'admin-dashboard':
        window.location.href = '/dashboard/admin';
        break;
      case 'user-management':
        window.location.href = '/dashboard/users';
        break;
      case 'proxy-management':
        window.location.href = '/dashboard/proxy-management';
        break;
      case 'system-settings':
        window.location.href = '/dashboard/settings';
        break;
      default:
        console.log('Menu clicked:', e.key);
    }
  };

  return (
    <Sider 
      width={250} 
      collapsed={collapsed}
      onCollapse={onCollapse}
      style={{ 
        backgroundColor: 'white',
        boxShadow: '2px 0 8px 0 rgba(29, 35, 41, 0.05)'
      }}
    >
      <div style={{ padding: collapsed ? '16px 8px' : '24px' }}>
        {/* Logo区域 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '32px'
        }}>
          {!collapsed ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '40px',
                height: '40px',
                background: 'linear-gradient(to right, #3b82f6, #4f46e5)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Globe className="text-white" size={20} />
              </div>
              <Title level={4} style={{ margin: 0, color: '#1f2937' }}>ProxyHub</Title>
            </div>
          ) : (
            <div style={{
              width: '32px',
              height: '32px',
              background: 'linear-gradient(to right, #3b82f6, #4f46e5)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Globe className="text-white" size={16} />
            </div>
          )}
        </div>
        
        {/* 用户菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={handleMenuClick}
          style={{ 
            border: 'none',
            backgroundColor: 'transparent'
          }}
          items={userMenuItems}
        />
        
        {/* 管理员菜单 */}
        {isAdmin && (
          <>
            <Divider style={{ margin: '16px 0' }}>
              {!collapsed && <span style={{ color: '#9ca3af', fontSize: '12px' }}>管理功能</span>}
            </Divider>
            <Menu
              mode="inline"
              selectedKeys={[selectedKey]}
              onClick={handleMenuClick}
              style={{ 
                border: 'none',
                backgroundColor: 'transparent'
              }}
              items={adminMenuItems}
            />
          </>
        )}
      </div>
    </Sider>
  );
}
