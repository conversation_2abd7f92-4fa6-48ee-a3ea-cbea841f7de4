'use client';

import React from 'react';
import { Layout, Typography, Button } from 'antd';
import { UserOutlined, DashboardOutlined } from '@ant-design/icons';
import { Globe } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/components/AuthProvider';

const { Header } = Layout;
const { Title } = Typography;

interface AppHeaderProps {
  currentPage?: 'home' | 'products' | 'pricing' | 'support';
}

export default function AppHeader({ currentPage = 'home' }: AppHeaderProps) {
  const { user } = useAuth();

  const getLinkClass = (page: string) => {
    return currentPage === page 
      ? "text-blue-600 font-medium" 
      : "text-gray-600 hover:text-gray-900 transition-colors";
  };

  return (
    <Header className="bg-white shadow-sm px-0" style={{ backgroundColor: 'white' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <Globe className="text-white" size={18} />
            </div>
            <Title level={3} className="!mb-0" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>
          </Link>
          
          <div className="flex items-center space-x-6">
            <Link href="/" className={getLinkClass('home')}>
              首页
            </Link>
            <Link href="/products" className={getLinkClass('products')}>
              产品服务
            </Link>
            <Link href="/pricing" className={getLinkClass('pricing')}>
              价格方案
            </Link>
            <Link href="/support" className={getLinkClass('support')}>
              技术支持
            </Link>
            
            <div className="flex items-center space-x-4 ml-4">
              {!user ? (
                <>
                  <Link href="/auth/login">
                    <Button type="text" icon={<UserOutlined />} style={{ color: '#374151' }}>
                      登录
                    </Button>
                  </Link>
                  <Link href="/auth/register">
                    <Button 
                      type="primary" 
                      className="bg-gradient-to-r from-blue-500 to-indigo-600 border-0"
                      style={{ background: 'linear-gradient(to right, #3b82f6, #4f46e5)', border: 'none' }}
                    >
                      免费注册
                    </Button>
                  </Link>
                </>
              ) : (
                <Link href="/dashboard">
                  <Button type="primary" icon={<DashboardOutlined />}>
                    控制台
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </Header>
  );
}
