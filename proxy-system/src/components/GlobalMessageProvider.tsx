'use client';

import React from 'react';
import CustomMessage, { useCustomMessage } from '@/components/CustomMessage';

interface GlobalMessageProviderProps {
  children: React.ReactNode;
}

const GlobalMessageProvider: React.FC<GlobalMessageProviderProps> = ({ children }) => {
  const { messages, onRemove } = useCustomMessage();

  return (
    <>
      {children}
      <CustomMessage messages={messages} onRemove={onRemove} />
    </>
  );
};

export default GlobalMessageProvider;
