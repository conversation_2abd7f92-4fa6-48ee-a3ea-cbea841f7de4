'use client';

import React from 'react';
import { Avatar, Dropdown, Typography } from 'antd';
import { 
  UserOutlined, 
  LogoutOutlined, 
  SettingOutlined,
  ProfileOutlined 
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Text } = Typography;

interface User {
  username: string;
  email: string;
  role: string;
}

interface UserDropdownProps {
  user: User;
  onLogout: () => void;
}

export default function UserDropdown({ user, onLogout }: UserDropdownProps) {
  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: onLogout,
    },
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    if (e.key === 'logout') {
      onLogout();
    } else {
      console.log('Menu clicked:', e.key);
      // 这里可以添加其他菜单项的处理逻辑
    }
  };

  return (
    <Dropdown
      menu={{ items: menuItems, onClick: handleMenuClick }}
      placement="bottomRight"
      arrow
      trigger={['click']}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '8px 12px',
          borderRadius: '8px',
          cursor: 'pointer',
          transition: 'background-color 0.2s',
          minWidth: 0,
          maxWidth: '280px'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#f3f4f6';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
      >
        <Avatar
          icon={<UserOutlined />}
          style={{
            backgroundColor: '#3b82f6',
            flexShrink: 0
          }}
        />
        <div
          style={{
            minWidth: 0,
            overflow: 'hidden',
            flex: 1
          }}
        >
          <div
            style={{
              color: '#1f2937',
              fontWeight: 600,
              fontSize: '14px',
              lineHeight: '20px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {user.username}
          </div>
          <div
            style={{
              color: '#6b7280',
              fontSize: '12px',
              lineHeight: '16px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {user.email}
          </div>
        </div>
      </div>
    </Dropdown>
  );
}
