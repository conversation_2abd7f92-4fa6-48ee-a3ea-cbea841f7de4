'use client';

import { useEffect } from 'react';
import { message, notification } from 'antd';

const GlobalMessageConfig: React.FC = () => {
  useEffect(() => {
    // 配置全局消息样式
    message.config({
      top: 100,
      duration: 3,
      maxCount: 3,
      rtl: false,
    });

    // 配置全局通知样式
    notification.config({
      placement: 'topRight',
      top: 100,
      duration: 4.5,
      rtl: false,
    });

    console.log('✅ 全局消息配置已初始化');
  }, []);

  return null; // 这个组件不渲染任何内容
};

export default GlobalMessageConfig;
