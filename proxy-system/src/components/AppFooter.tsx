'use client';

import React from 'react';
import { Layout, Typography, Row, Col, Divider } from 'antd';
import { Globe, Users, Clock } from 'lucide-react';
import Link from 'next/link';

const { Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function AppFooter() {
  return (
    <Footer className="bg-gray-900 text-white" style={{ backgroundColor: '#111827', color: 'white' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Row gutter={[48, 32]}>
          <Col xs={24} sm={12} lg={6}>
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <Globe className="text-white" size={18} />
              </div>
              <Title level={4} className="!mb-0" style={{ color: 'white', margin: 0 }}>ProxyHub</Title>
            </div>
            <Paragraph className="!mb-6" style={{ color: '#9ca3af', marginBottom: '1.5rem' }}>
              专业的代理服务平台，为您的业务提供稳定可靠的网络代理解决方案。
            </Paragraph>
            <div className="flex space-x-4">
              <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors">
                <Globe className="text-gray-400" size={20} />
              </div>
              <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors">
                <Users className="text-gray-400" size={20} />
              </div>
            </div>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>产品服务</Title>
            <div className="space-y-3">
              <div>
                <Link href="/products" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  IPv4 代理
                </Link>
              </div>
              <div>
                <Link href="/products" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  IPv6 代理
                </Link>
              </div>
              <div>
                <Link href="/products" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  共享代理
                </Link>
              </div>
              <div>
                <Link href="/pricing" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  企业定制
                </Link>
              </div>
            </div>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>支持中心</Title>
            <div className="space-y-3">
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  使用文档
                </Link>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  API 接口
                </Link>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  常见问题
                </Link>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  技术支持
                </Link>
              </div>
            </div>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>联系我们</Title>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Clock className="text-gray-400" size={16} />
                <Text style={{ color: '#9ca3af' }}>7×24小时服务</Text>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  在线客服
                </Link>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  工单系统
                </Link>
              </div>
              <div>
                <Link href="/support" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
                  商务合作
                </Link>
              </div>
            </div>
          </Col>
        </Row>
        
        <Divider style={{ borderColor: '#374151', margin: '2rem 0' }} />
        
        <div className="flex flex-col md:flex-row justify-between items-center">
          <Text style={{ color: '#9ca3af' }}>
            © 2024 ProxyHub. 保留所有权利.
          </Text>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacy" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
              隐私政策
            </Link>
            <Link href="/terms" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
              服务条款
            </Link>
            <Link href="/legal" style={{ color: '#9ca3af' }} className="hover:text-white transition-colors">
              法律声明
            </Link>
          </div>
        </div>
      </div>
    </Footer>
  );
}
