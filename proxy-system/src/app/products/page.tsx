'use client';

import React from 'react';
import { 
  Layout, 
  Typography, 
  Card, 
  Row, 
  Col, 
  Button, 
  Table, 
  Tag,
  Space,
  Divider
} from 'antd';
import { 
  Globe,
  Wifi,
  Users,
  Award,
  CheckCircle,
  ArrowRight,
  Star
} from 'lucide-react';
import Link from 'next/link';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function ProductsPage() {
  const pricingData = [
    {
      key: '1',
      type: 'IPv4 独享',
      description: '独享IP地址，高速稳定',
      price: '¥15/天',
      features: ['独享带宽', '99.9%可用性', 'HTTP/SOCKS5', '24/7支持'],
      popular: true
    },
    {
      key: '2',
      type: 'IPv6 代理',
      description: '下一代协议，价格优惠',
      price: '¥8/天',
      features: ['海量IP池', '高性价比', '未来标准', '批量优惠'],
      popular: false
    },
    {
      key: '3',
      type: '共享代理',
      description: '经济实惠，功能完整',
      price: '¥5/天',
      features: ['基础功能', '入门首选', '快速部署', '成本低廉'],
      popular: false
    }
  ];

  const columns = [
    {
      title: '代理类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string, record: any) => (
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${record.popular ? 'bg-orange-500' : 'bg-blue-500'}`}></div>
          <span className="font-medium">{text}</span>
          {record.popular && <Tag color="orange">推荐</Tag>}
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (text: string) => <Text strong className="text-lg text-blue-600">{text}</Text>,
    },
    {
      title: '特性',
      dataIndex: 'features',
      key: 'features',
      render: (features: string[]) => (
        <div className="space-y-1">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-1">
              <CheckCircle size={14} className="text-green-500" />
              <Text className="text-sm">{feature}</Text>
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Link href="/auth/register">
          <Button type="primary" icon={<ArrowRight size={16} />}>
            立即购买
          </Button>
        </Link>
      ),
    },
  ];

  return (
    <Layout className="min-h-screen">
      {/* 导航栏 */}
      <Header className="bg-white shadow-sm px-0" style={{ backgroundColor: 'white' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <Globe className="text-white" size={18} />
              </div>
              <Title level={3} className="!mb-0" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-gray-600 hover:text-gray-900">首页</Link>
              <Link href="/products" className="text-blue-600 font-medium">产品服务</Link>
              <Link href="/pricing" className="text-gray-600 hover:text-gray-900">价格方案</Link>
              <Link href="/support" className="text-gray-600 hover:text-gray-900">技术支持</Link>
              <Link href="/auth/login">
                <Button type="text">登录</Button>
              </Link>
              <Link href="/auth/register">
                <Button type="primary">注册</Button>
              </Link>
            </div>
          </div>
        </div>
      </Header>

      <Content>
        {/* 页面标题 */}
        <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={1} className="!text-4xl !font-bold !text-gray-900 !mb-4">
              产品服务
            </Title>
            <Paragraph className="!text-xl !text-gray-600 !max-w-3xl !mx-auto">
              提供多种类型的代理服务，满足不同业务需求。从个人使用到企业级应用，
              我们都有相应的解决方案。
            </Paragraph>
          </div>
        </section>

        {/* 产品特性 */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Row gutter={[48, 48]}>
              <Col xs={24} lg={8}>
                <Card className="text-center h-full border-0 shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Award className="text-white" size={32} />
                  </div>
                  <Title level={4}>IPv4 独享代理</Title>
                  <Paragraph className="text-gray-600">
                    独享IP地址，提供最佳性能和稳定性。适合对速度和稳定性要求较高的业务场景。
                  </Paragraph>
                  <ul className="text-left space-y-2 mt-4">
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />独享带宽，无共享风险</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />99.9% 服务可用性</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />支持HTTP/HTTPS和SOCKS5</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />24/7 技术支持</li>
                  </ul>
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Card className="text-center h-full border-0 shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Wifi className="text-white" size={32} />
                  </div>
                  <Title level={4}>IPv6 代理</Title>
                  <Paragraph className="text-gray-600">
                    下一代互联网协议，提供更大的地址空间和更好的性能，价格更优惠。
                  </Paragraph>
                  <ul className="text-left space-y-2 mt-4">
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />海量IP资源池</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />价格更加优惠</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />未来网络标准</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />批量购买优惠</li>
                  </ul>
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Card className="text-center h-full border-0 shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="text-white" size={32} />
                  </div>
                  <Title level={4}>共享代理</Title>
                  <Paragraph className="text-gray-600">
                    经济实惠的共享IPv4代理，适合预算有限的用户，功能完整。
                  </Paragraph>
                  <ul className="text-left space-y-2 mt-4">
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />价格经济实惠</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />基础功能完整</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />适合入门用户</li>
                    <li className="flex items-center"><CheckCircle size={16} className="text-green-500 mr-2" />快速部署使用</li>
                  </ul>
                </Card>
              </Col>
            </Row>
          </div>
        </section>

        {/* 价格对比表 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <Title level={2} className="!text-3xl !font-bold !text-gray-900">
                价格对比
              </Title>
              <Paragraph className="!text-lg !text-gray-600">
                选择最适合您需求的代理服务方案
              </Paragraph>
            </div>
            
            <Card className="shadow-lg">
              <Table 
                columns={columns} 
                dataSource={pricingData} 
                pagination={false}
                className="custom-table"
              />
            </Card>
            
            <div className="text-center mt-8">
              <Text className="text-gray-500">
                * 价格仅供参考，实际价格可能因促销活动而有所调整
              </Text>
            </div>
          </div>
        </section>

        {/* CTA区域 */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={2} className="!text-3xl !font-bold !text-white !mb-4">
              准备开始使用？
            </Title>
            <Paragraph className="!text-xl !text-blue-100 !mb-8">
              立即注册，享受专业的代理服务
            </Paragraph>
            <Space size="large">
              <Link href="/auth/register">
                <Button 
                  type="primary" 
                  size="large" 
                  className="h-12 px-8 text-lg bg-white text-blue-600 border-0 hover:bg-gray-100"
                >
                  免费注册
                </Button>
              </Link>
              <Link href="/pricing">
                <Button 
                  size="large" 
                  className="h-12 px-8 text-lg text-white border-white hover:bg-white hover:text-blue-600"
                >
                  查看价格
                </Button>
              </Link>
            </Space>
          </div>
        </section>
      </Content>

      {/* 简化的页脚 */}
      <Footer className="bg-gray-900 text-center py-8" style={{ backgroundColor: '#111827' }}>
        <Text style={{ color: '#9ca3af' }}>
          © 2024 ProxyHub. 保留所有权利.
        </Text>
      </Footer>
    </Layout>
  );
}
