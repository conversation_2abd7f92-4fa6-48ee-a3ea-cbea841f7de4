'use client';

import React from 'react';
import { Button, Card, Space, Modal } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import { customMessage } from '@/components/CustomMessage';

export default function LogoutTestPage() {
  const { logout, user } = useAuth();
  const router = useRouter();

  const testLogoutDirect = async () => {
    console.log('🔍 直接测试logout函数');
    
    const hideLoading = customMessage.loading('正在测试退出登录...');
    
    try {
      await logout();
      hideLoading();
      customMessage.success('退出登录测试成功！');
      console.log('✅ 直接logout测试成功');
    } catch (error) {
      hideLoading();
      customMessage.error('退出登录测试失败');
      console.error('❌ 直接logout测试失败:', error);
    }
  };

  const testLogoutWithConfirm = () => {
    console.log('🔍 测试带确认对话框的退出登录');
    
    Modal.confirm({
      title: '确认退出登录',
      content: '您确定要退出登录吗？',
      okText: '确认退出',
      cancelText: '取消',
      centered: true,
      icon: <LogoutOutlined />,
      onOk: async () => {
        console.log('🔍 用户确认退出');
        
        const hideLoading = customMessage.loading('正在退出登录...');
        
        try {
          await logout();
          hideLoading();
          customMessage.success('已成功退出登录！');
          
          setTimeout(() => {
            console.log('🔍 跳转到首页');
            router.push('/');
          }, 2000);
          
        } catch (error) {
          hideLoading();
          customMessage.error('退出登录失败');
          console.error('❌ 退出登录失败:', error);
        }
      },
      onCancel: () => {
        console.log('🔍 用户取消退出');
      }
    });
  };

  const testModalOnly = () => {
    console.log('🔍 测试Modal.confirm功能');
    
    Modal.confirm({
      title: '测试对话框',
      content: '这是一个测试对话框',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log('✅ Modal.confirm正常工作');
        customMessage.success('Modal.confirm测试成功');
      },
      onCancel: () => {
        console.log('🔍 用户取消了测试对话框');
      }
    });
  };

  return (
    <div style={{ padding: '50px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="退出登录功能测试">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <h3>当前用户信息</h3>
            {user ? (
              <div>
                <p>用户名: {user.username}</p>
                <p>邮箱: {user.email}</p>
                <p>角色: {user.role}</p>
              </div>
            ) : (
              <p>未登录</p>
            )}
          </div>
          
          <div>
            <h3>测试功能</h3>
            <Space wrap>
              <Button 
                type="primary" 
                onClick={testModalOnly}
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
              >
                测试Modal对话框
              </Button>
              
              <Button 
                type="primary" 
                onClick={testLogoutDirect}
                style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
              >
                直接测试退出登录
              </Button>
              
              <Button 
                danger
                icon={<LogoutOutlined />}
                onClick={testLogoutWithConfirm}
              >
                完整退出登录流程
              </Button>
            </Space>
          </div>
          
          <div>
            <h4>测试说明：</h4>
            <ol>
              <li><strong>测试Modal对话框</strong>: 验证Modal.confirm是否正常工作</li>
              <li><strong>直接测试退出登录</strong>: 直接调用logout函数，不显示确认对话框</li>
              <li><strong>完整退出登录流程</strong>: 模拟完整的退出登录流程，包括确认对话框</li>
            </ol>
          </div>
          
          <div>
            <h4>预期效果：</h4>
            <ul>
              <li>点击"测试Modal对话框"应该显示确认对话框</li>
              <li>点击"直接测试退出登录"应该立即执行退出登录</li>
              <li>点击"完整退出登录流程"应该显示确认对话框，确认后执行退出登录并跳转</li>
              <li>所有操作都应该在控制台显示详细日志</li>
            </ul>
          </div>
          
          <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
            <strong>调试信息：</strong>
            <br />
            请打开浏览器开发者工具查看控制台日志
            <br />
            如果退出登录成功，页面应该跳转到首页，用户状态应该变为未登录
          </div>
        </Space>
      </Card>
    </div>
  );
}
