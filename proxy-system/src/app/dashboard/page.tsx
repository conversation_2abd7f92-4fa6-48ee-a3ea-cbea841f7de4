'use client';

import React from 'react';
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Button, 
  Typography, 
  Space,
  Avatar,
  Divider
} from 'antd';
import { 
  UserOutlined, 
  GlobalOutlined, 
  ShoppingCartOutlined,
  WalletOutlined,
  SettingOutlined,
  LogoutOutlined,
  PlusOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import LoadingScreen from '@/components/LoadingScreen';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

export default function DashboardPage() {
  const { user, loading, logout, isAdmin } = useAuth();
  const router = useRouter();

  if (loading) {
    return <LoadingScreen message="正在加载控制台..." />;
  }

  if (!user) {
    router.push('/auth/login');
    return <LoadingScreen message="正在跳转到登录页面..." />;
  }

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  return (
    <Layout className="min-h-screen">
      {/* 侧边栏 */}
      <Sider width={250} className="bg-white shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <GlobalOutlined className="text-white text-lg" />
            </div>
            <Title level={4} className="!mb-0">ProxyHub</Title>
          </div>
          
          <div className="space-y-2">
            <Button type="text" icon={<BarChartOutlined />} className="w-full justify-start">
              概览
            </Button>
            <Button type="text" icon={<GlobalOutlined />} className="w-full justify-start">
              我的代理
            </Button>
            <Button type="text" icon={<ShoppingCartOutlined />} className="w-full justify-start">
              购买代理
            </Button>
            <Button type="text" icon={<WalletOutlined />} className="w-full justify-start">
              账户充值
            </Button>
            {isAdmin && (
              <>
                <Divider />
                <Button type="text" icon={<SettingOutlined />} className="w-full justify-start">
                  系统管理
                </Button>
              </>
            )}
          </div>
        </div>
      </Sider>

      <Layout>
        {/* 顶部导航 */}
        <Header className="bg-white shadow-sm px-6 flex justify-between items-center">
          <Title level={3} className="!mb-0">控制台</Title>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <Avatar icon={<UserOutlined />} />
              <div>
                <Text strong>{user.username}</Text>
                <br />
                <Text className="text-gray-500 text-sm">{user.email}</Text>
              </div>
            </div>
            <Button 
              type="text" 
              icon={<LogoutOutlined />} 
              onClick={handleLogout}
              className="text-gray-600"
            >
              退出
            </Button>
          </div>
        </Header>

        {/* 主要内容 */}
        <Content className="p-6 bg-gray-50">
          <div className="mb-6">
            <Title level={2}>欢迎回来，{user.username}！</Title>
            <Text className="text-gray-600">
              这里是您的代理服务控制台，您可以管理代理、查看统计信息和进行各种操作。
            </Text>
          </div>

          {/* 统计卡片 */}
          <Row gutter={[24, 24]} className="mb-8">
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="账户余额"
                  value={user.balance}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="活跃代理"
                  value={0}
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="本月使用"
                  value={0}
                  suffix="GB"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总订单"
                  value={0}
                  suffix="个"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 快速操作 */}
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={16}>
              <Card title="快速操作" className="h-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    size="large"
                    className="h-16 bg-gradient-to-r from-blue-500 to-indigo-600 border-0"
                  >
                    购买新代理
                  </Button>
                  <Button 
                    icon={<WalletOutlined />} 
                    size="large"
                    className="h-16"
                  >
                    账户充值
                  </Button>
                  <Button 
                    icon={<GlobalOutlined />} 
                    size="large"
                    className="h-16"
                  >
                    查看代理列表
                  </Button>
                  <Button 
                    icon={<BarChartOutlined />} 
                    size="large"
                    className="h-16"
                  >
                    使用统计
                  </Button>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={8}>
              <Card title="账户信息" className="h-full">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <Text>用户类型：</Text>
                    <Text strong className={isAdmin ? 'text-red-600' : 'text-blue-600'}>
                      {isAdmin ? '管理员' : '普通用户'}
                    </Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>注册时间：</Text>
                    <Text>{new Date(user.created_at).toLocaleDateString()}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>账户状态：</Text>
                    <Text className="text-green-600">正常</Text>
                  </div>
                  <Divider />
                  <Button type="link" className="p-0">
                    修改密码
                  </Button>
                  <br />
                  <Button type="link" className="p-0">
                    账户设置
                  </Button>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 最近活动 */}
          <Row gutter={[24, 24]} className="mt-8">
            <Col span={24}>
              <Card title="最近活动">
                <div className="text-center py-12 text-gray-500">
                  <GlobalOutlined className="text-4xl mb-4" />
                  <div>暂无活动记录</div>
                  <Text className="text-sm">开始使用代理服务后，这里将显示您的活动记录</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
}
