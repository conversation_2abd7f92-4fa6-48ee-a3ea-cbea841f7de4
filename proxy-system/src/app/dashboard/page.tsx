'use client';

import React from 'react';
import {
  Layout,
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Typography,
  Space,
  Divider,
  Modal
} from 'antd';
import {
  PlusOutlined,
  GlobalOutlined,
  ShoppingCartOutlined,
  WalletOutlined,
  BarChartOutlined,
  HistoryOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import LoadingScreen from '@/components/LoadingScreen';
import DashboardSidebar from '@/components/DashboardSidebar';
import UserDropdown from '@/components/UserDropdown';
import { customMessage } from '@/components/CustomMessage';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

export default function DashboardPage() {
  const { user, loading, logout, isAdmin } = useAuth();
  const router = useRouter();

  if (loading) {
    return <LoadingScreen message="正在加载控制台..." />;
  }

  if (!user) {
    router.push('/auth/login');
    return <LoadingScreen message="正在跳转到登录页面..." />;
  }

  const handleLogout = async () => {
    console.log('🔍 退出登录按钮被点击');

    // 显示确认对话框
    Modal.confirm({
      title: '确认退出登录',
      content: '您确定要退出登录吗？',
      okText: '确认退出',
      cancelText: '取消',
      centered: true,
      icon: <LogoutOutlined />,
      onOk: async () => {
        console.log('🔍 用户确认退出登录');

        // 显示退出中的提示
        console.log('🔍 显示退出加载消息');
        const hideLoading = customMessage.loading('正在退出登录...');

        try {
          console.log('🔍 调用logout函数');
          await logout();
          console.log('✅ logout函数调用成功');

          hideLoading();

          // 显示成功提示
          console.log('🔍 显示退出成功消息');
          customMessage.success('已成功退出登录，感谢您的使用！');

          // 延迟跳转到首页
          console.log('🔍 准备跳转到首页');
          setTimeout(() => {
            console.log('🔍 执行跳转到首页');
            router.push('/');
          }, 2000); // 增加延迟时间便于观察

        } catch (error) {
          console.error('❌ 退出登录失败:', error);
          hideLoading();
          customMessage.error('退出登录失败，请重试');
        }
      },
      onCancel: () => {
        console.log('🔍 用户取消退出登录');
      }
    });
  };

  return (
    <Layout className="min-h-screen">
      <DashboardSidebar isAdmin={isAdmin} />

      <Layout>
        {/* 顶部导航 */}
        <Header
          style={{
            backgroundColor: 'white',
            padding: '0 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            height: '64px',
            lineHeight: '64px'
          }}
        >
          <Title
            level={3}
            style={{
              margin: 0,
              color: '#1f2937',
              fontSize: '18px',
              fontWeight: 600,
              flexShrink: 0
            }}
          >
            控制台
          </Title>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            minWidth: 0,
            flex: 1
          }}>
            <UserDropdown
              user={{
                username: user.username,
                email: user.email,
                role: user.role
              }}
              onLogout={handleLogout}
            />
          </div>
        </Header>

        {/* 主要内容 */}
        <Content style={{ padding: '24px', backgroundColor: '#f5f5f5' }}>
          <div style={{ marginBottom: '24px' }}>
            <Title level={2} style={{ color: '#1f2937', marginBottom: '8px' }}>
              欢迎回来，{user.username}！
            </Title>
            <Text style={{ color: '#6b7280', fontSize: '16px' }}>
              这里是您的代理服务控制台，您可以管理代理、查看统计信息和进行各种操作。
            </Text>
          </div>

          {/* 统计卡片 */}
          <Row gutter={[24, 24]} className="mb-8">
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="账户余额"
                  value={user.balance}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="活跃代理"
                  value={0}
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="本月使用"
                  value={0}
                  suffix="GB"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总订单"
                  value={0}
                  suffix="个"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 快速操作 */}
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={16}>
              <Card title="快速操作" style={{ height: '100%' }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px'
                }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    size="large"
                    style={{
                      height: '64px',
                      background: 'linear-gradient(to right, #3b82f6, #4f46e5)',
                      border: 'none',
                      fontSize: '16px'
                    }}
                  >
                    购买新代理
                  </Button>
                  <Button
                    icon={<WalletOutlined />}
                    size="large"
                    style={{
                      height: '64px',
                      fontSize: '16px'
                    }}
                  >
                    账户充值
                  </Button>
                  <Button
                    icon={<GlobalOutlined />}
                    size="large"
                    style={{
                      height: '64px',
                      fontSize: '16px'
                    }}
                  >
                    查看代理列表
                  </Button>
                  <Button
                    icon={<BarChartOutlined />}
                    size="large"
                    style={{
                      height: '64px',
                      fontSize: '16px'
                    }}
                  >
                    使用统计
                  </Button>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={8}>
              <Card title="账户信息" style={{ height: '100%' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text style={{ color: '#6b7280' }}>用户类型：</Text>
                    <Text strong style={{ color: isAdmin ? '#dc2626' : '#3b82f6' }}>
                      {isAdmin ? '管理员' : '普通用户'}
                    </Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text style={{ color: '#6b7280' }}>注册时间：</Text>
                    <Text style={{ color: '#374151' }}>{new Date(user.created_at).toLocaleDateString()}</Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text style={{ color: '#6b7280' }}>账户状态：</Text>
                    <Text style={{ color: '#059669' }}>正常</Text>
                  </div>
                  <Divider style={{ margin: '16px 0' }} />
                  <Button type="link" style={{ padding: 0, color: '#3b82f6' }}>
                    修改密码
                  </Button>
                  <br />
                  <Button type="link" style={{ padding: 0, color: '#3b82f6' }}>
                    账户设置
                  </Button>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 最近活动 */}
          <Row gutter={[24, 24]} className="mt-8">
            <Col span={24}>
              <Card title="最近活动">
                <div className="text-center py-12" style={{ color: '#9ca3af' }}>
                  <HistoryOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#d1d5db' }} />
                  <div style={{ fontSize: '16px', marginBottom: '8px', color: '#6b7280' }}>暂无活动记录</div>
                  <Text style={{ fontSize: '14px', color: '#9ca3af' }}>开始使用代理服务后，这里将显示您的活动记录</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
}
