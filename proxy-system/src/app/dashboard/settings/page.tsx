'use client';

import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Form,
  Input,
  Button,
  Typography,
  message,
  Space,
  Divider,
  Alert,
  Switch,
  InputNumber
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import DashboardSidebar from '@/components/DashboardSidebar';
import UserDropdown from '@/components/UserDropdown';
import { settingsApi, apiRequest } from '@/lib/api';
import { settingsMessages } from '@/lib/notifications';

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface SystemSettings {
  px6_api_key: string;
  px6_api_url: string;
  max_proxies_per_user: number;
  default_proxy_duration: number;
  enable_auto_renewal: boolean;
  maintenance_mode: boolean;
  system_notice: string;
}

export default function SettingsPage() {
  const { user, loading: authLoading, logout } = useAuth();
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [dbStatus, setDbStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');
  const [settings, setSettings] = useState<SystemSettings>({
    px6_api_key: '',
    px6_api_url: 'https://api.px6.io',
    max_proxies_per_user: 10,
    default_proxy_duration: 30,
    enable_auto_renewal: true,
    maintenance_mode: false,
    system_notice: ''
  });

  // 检查管理员权限
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    // 等待认证加载完成
    if (authLoading) {
      return;
    }

    // 检查用户是否已登录
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // 检查管理员权限
    if (!isAdmin) {
      router.push('/dashboard');
      return;
    }

    // 加载设置和检查数据库状态
    loadSettings();
    checkDatabaseStatus();
  }, [user, isAdmin, router, authLoading]);

  const loadSettings = async () => {
    try {
      const data = await settingsApi.getSettings();
      setSettings(data);
      form.setFieldsValue(data);
    } catch (error) {
      console.error('Failed to load settings:', error);
      settingsMessages.loadError();
    }
  };

  const handleSave = async (values: SystemSettings) => {
    setLoading(true);

    // 显示保存中的提示
    const hideLoading = settingsMessages.saveLoading();

    try {
      await settingsApi.saveSettings(values);
      hideLoading();

      // 显示成功提示
      settingsMessages.saveSuccess();

      setSettings(values);
    } catch (error: any) {
      hideLoading();

      // 显示错误提示
      settingsMessages.saveError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    router.push('/auth/login');
  };

  const checkDatabaseStatus = async () => {
    try {
      const response = await apiRequest('/api/admin/init-db');
      setDbStatus('connected');
    } catch (error) {
      setDbStatus('disconnected');
    }
  };

  const initializeDatabase = async () => {
    setLoading(true);
    try {
      await apiRequest('/api/admin/init-db', { method: 'POST' });
      settingsMessages.dbInitSuccess();
      setDbStatus('connected');
    } catch (error: any) {
      settingsMessages.dbInitError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const testApiConnection = async () => {
    const apiKey = form.getFieldValue('px6_api_key');
    const apiUrl = form.getFieldValue('px6_api_url');

    if (!apiKey) {
      settingsMessages.apiKeyRequired();
      return;
    }

    setLoading(true);

    // 显示测试中的提示
    const hideLoading = settingsMessages.apiTestLoading();

    try {
      const result = await settingsApi.testPx6Connection(apiKey, apiUrl);
      hideLoading();

      if (result.success) {
        const info = result.accountInfo ? `账户余额: ${result.accountInfo.balance}` : '';
        settingsMessages.apiTestSuccess(info);
      } else {
        settingsMessages.apiTestError(result.message);
      }
    } catch (error: any) {
      hideLoading();
      settingsMessages.apiTestError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // 显示加载状态
  if (authLoading) {
    return (
      <Layout className="min-h-screen">
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: '16px'
        }}>
          <div style={{ fontSize: '16px', color: '#666' }}>
            正在加载用户信息...
          </div>
        </div>
      </Layout>
    );
  }

  // 检查用户权限
  if (!user || !isAdmin) {
    return null;
  }

  return (
    <Layout className="min-h-screen">
      <DashboardSidebar isAdmin={isAdmin} />
      
      <Layout>
        <Header 
          style={{ 
            backgroundColor: 'white', 
            padding: '0 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            height: '64px',
            lineHeight: '64px'
          }}
        >
          <Title 
            level={3} 
            style={{ 
              margin: 0, 
              color: '#1f2937',
              fontSize: '18px',
              fontWeight: 600,
              flexShrink: 0
            }}
          >
            <SettingOutlined style={{ marginRight: '8px' }} />
            系统设置
          </Title>
          
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'flex-end',
            minWidth: 0,
            flex: 1
          }}>
            <UserDropdown 
              user={{
                username: user.username,
                email: user.email,
                role: user.role
              }}
              onLogout={handleLogout}
            />
          </div>
        </Header>

        <Content style={{ padding: '24px', backgroundColor: '#f5f5f5' }}>
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <Alert
              message="管理员设置"
              description="这些设置将影响整个系统的运行，请谨慎修改。"
              type="info"
              showIcon
              style={{ marginBottom: '24px' }}
            />

            {/* 数据库状态 */}
            <Card
              title="数据库状态"
              style={{ marginBottom: '24px' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div
                    style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: dbStatus === 'connected' ? '#52c41a' : dbStatus === 'disconnected' ? '#ff4d4f' : '#faad14'
                    }}
                  />
                  <Text>
                    数据库状态: {
                      dbStatus === 'connected' ? '已连接' :
                      dbStatus === 'disconnected' ? '未连接' :
                      '检查中...'
                    }
                  </Text>
                </div>
                <Space>
                  <Button onClick={checkDatabaseStatus} loading={loading}>
                    检查状态
                  </Button>
                  {dbStatus === 'disconnected' && (
                    <Button type="primary" onClick={initializeDatabase} loading={loading}>
                      初始化数据库
                    </Button>
                  )}
                </Space>
              </div>
            </Card>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={settings}
            >
              {/* PX6 API配置 */}
              <Card 
                title={
                  <Space>
                    <KeyOutlined />
                    PX6 API配置
                  </Space>
                }
                style={{ marginBottom: '24px' }}
              >
                <Form.Item
                  name="px6_api_key"
                  label="PX6 API密钥"
                  rules={[
                    { required: true, message: '请输入PX6 API密钥' },
                    { min: 10, message: 'API密钥长度至少10个字符' }
                  ]}
                >
                  <Input.Password
                    placeholder="请输入PX6 API密钥"
                    visibilityToggle={{
                      visible: showApiKey,
                      onVisibleChange: setShowApiKey,
                    }}
                    iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

                <Form.Item
                  name="px6_api_url"
                  label="PX6 API地址"
                  rules={[
                    { required: true, message: '请输入PX6 API地址' },
                    { type: 'url', message: '请输入有效的URL地址' }
                  ]}
                >
                  <Input placeholder="https://api.px6.io" />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="default" 
                    onClick={testApiConnection}
                    loading={loading}
                  >
                    测试API连接
                  </Button>
                </Form.Item>
              </Card>

              {/* 系统配置 */}
              <Card 
                title="系统配置"
                style={{ marginBottom: '24px' }}
              >
                <Form.Item
                  name="max_proxies_per_user"
                  label="每用户最大代理数量"
                  rules={[{ required: true, message: '请输入最大代理数量' }]}
                >
                  <InputNumber min={1} max={100} style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="default_proxy_duration"
                  label="默认代理时长（天）"
                  rules={[{ required: true, message: '请输入默认代理时长' }]}
                >
                  <InputNumber min={1} max={365} style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="enable_auto_renewal"
                  label="启用自动续费"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name="maintenance_mode"
                  label="维护模式"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Card>

              {/* 系统通知 */}
              <Card 
                title="系统通知"
                style={{ marginBottom: '24px' }}
              >
                <Form.Item
                  name="system_notice"
                  label="系统公告"
                  extra="将在用户控制台顶部显示"
                >
                  <TextArea 
                    rows={4} 
                    placeholder="输入系统公告内容..."
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
              </Card>

              {/* 保存按钮 */}
              <Card>
                <Form.Item style={{ marginBottom: 0 }}>
                  <Space>
                    <Button 
                      type="primary" 
                      htmlType="submit" 
                      loading={loading}
                      icon={<SaveOutlined />}
                      size="large"
                    >
                      保存设置
                    </Button>
                    <Button 
                      onClick={() => form.resetFields()}
                      size="large"
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Card>
            </Form>
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
