import type { Metadata } from "next";
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from '@/components/AuthProvider';
import DevModeIndicator from '@/components/DevModeIndicator';
import AuthDebugger from '@/components/AuthDebugger';
import GlobalMessageConfig from '@/components/GlobalMessageConfig';
import "./globals.css";

export const metadata: Metadata = {
  title: "代理系统",
  description: "PX6代理购买和管理系统",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body>
        <AntdRegistry>
          <ConfigProvider locale={zhCN}>
            <AuthProvider>
              <GlobalMessageConfig />
              <DevModeIndicator />
              {children}
              <AuthDebugger />
            </AuthProvider>
          </ConfigProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
