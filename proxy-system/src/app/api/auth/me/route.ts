import { NextRequest, NextResponse } from 'next/server';
import { createApiResponse } from '@/lib/utils';
import { verifyUserToken } from '@/lib/auth-demo';

export async function GET(request: NextRequest) {
  try {
    // 从cookie或Authorization header获取token
    const token = request.cookies.get('token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        createApiResponse(false, null, null, '未提供认证令牌'),
        { status: 401 }
      );
    }

    // 使用演示版本验证token并获取用户信息
    const user = verifyUserToken(token);

    return NextResponse.json(
      createApiResponse(true, user, '获取用户信息成功')
    );

  } catch (error: any) {
    console.error('Get user info error:', error);
    return NextResponse.json(
      createApiResponse(false, null, null, error.message || '获取用户信息失败'),
      { status: 401 }
    );
  }
}
