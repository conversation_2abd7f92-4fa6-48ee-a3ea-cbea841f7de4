import { NextRequest, NextResponse } from 'next/server';
import { createApiResponse, isValidEmail, isValidPassword } from '@/lib/utils';
import { registerUser } from '@/lib/auth-demo';
import { RegisterRequest } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json();
    const { email, username, password } = body;

    // 验证输入
    if (!email || !username || !password) {
      return NextResponse.json(
        createApiResponse(false, null, null, '所有字段都是必填的'),
        { status: 400 }
      );
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json(
        createApiResponse(false, null, null, '邮箱格式不正确'),
        { status: 400 }
      );
    }

    // 验证密码强度
    const passwordValidation = isValidPassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        createApiResponse(false, null, null, passwordValidation.message),
        { status: 400 }
      );
    }

    // 使用演示版本的注册
    const result = await registerUser(email, username, password);

    const response = NextResponse.json(
      createApiResponse(true, result, '注册成功')
    );

    // 设置HTTP-only cookie
    response.cookies.set('token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7天
    });

    return response;

  } catch (error: any) {
    console.error('Register error:', error);
    return NextResponse.json(
      createApiResponse(false, null, null, error.message || '注册失败'),
      { status: 400 }
    );
  }
}
        createApiResponse(false, null, null, passwordValidation.errors.join(', ')),
        { status: 400 }
      );
    }

    // 检查用户是否已存在
    const existingUsers = await query(
      'SELECT id FROM users WHERE email = ?',
      [email]
    ) as User[];

    if (existingUsers.length > 0) {
      return NextResponse.json(
        createApiResponse(false, null, null, '该邮箱已被注册'),
        { status: 409 }
      );
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const result = await query(
      'INSERT INTO users (email, username, password, balance, role) VALUES (?, ?, ?, ?, ?)',
      [email, username, hashedPassword, 0.00, 'user']
    ) as any;

    const userId = result.insertId;

    // 生成JWT token
    const token = generateToken({
      userId,
      email,
      role: 'user'
    });

    // 返回用户信息
    const userInfo = {
      id: userId,
      email,
      username,
      balance: 0.00,
      role: 'user' as const
    };

    const response = NextResponse.json(
      createApiResponse(true, { user: userInfo, token }, '注册成功')
    );

    // 设置HTTP-only cookie
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7天
    });

    return response;

  } catch (error) {
    console.error('Register error:', error);
    return NextResponse.json(
      createApiResponse(false, null, null, '服务器内部错误'),
      { status: 500 }
    );
  }
}
