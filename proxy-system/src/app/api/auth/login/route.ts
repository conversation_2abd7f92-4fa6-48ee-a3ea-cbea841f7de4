import { NextRequest, NextResponse } from 'next/server';
import { createApiResponse } from '@/lib/utils';
import { loginUser, initDefaultUsers } from '@/lib/auth-demo';
import { LoginRequest } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // 初始化默认用户（如果不存在）
    initDefaultUsers();

    const body: LoginRequest = await request.json();
    const { email, password } = body;

    // 验证输入
    if (!email || !password) {
      return NextResponse.json(
        createApiResponse(false, null, null, '邮箱和密码不能为空'),
        { status: 400 }
      );
    }

    // 使用演示版本的登录
    const result = await loginUser(email, password);

    const response = NextResponse.json(
      createApiResponse(true, result, '登录成功')
    );

    // 设置HTTP-only cookie
    response.cookies.set('token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7天
    });

    return response;

  } catch (error: any) {
    console.error('Login error:', error);
    return NextResponse.json(
      createApiResponse(false, null, null, error.message || '登录失败'),
      { status: 401 }
    );
  }
}
