import { NextRequest, NextResponse } from 'next/server';
import { verifyUserToken } from '@/lib/auth-demo';

// 验证管理员权限（演示版本）
function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供有效的认证token');
  }

  const token = authHeader.substring(7);
  const user = verifyUserToken(token);

  if (user.role !== 'admin') {
    throw new Error('需要管理员权限');
  }

  return user;
}

// 测试PX6 API连接
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    verifyAdmin(request);

    const { apiKey, apiUrl } = await request.json();

    if (!apiKey || !apiUrl) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 测试API连接
    try {
      const testResponse = await fetch(`${apiUrl}/v1/account/info`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10秒超时
      });

      if (testResponse.ok) {
        const data = await testResponse.json();
        return NextResponse.json({ 
          success: true, 
          message: 'API连接成功',
          accountInfo: {
            balance: data.balance || 'N/A',
            plan: data.plan || 'N/A'
          }
        });
      } else {
        return NextResponse.json({ 
          success: false, 
          message: `API连接失败: ${testResponse.status} ${testResponse.statusText}` 
        }, { status: 400 });
      }
    } catch (fetchError: any) {
      console.error('PX6 API测试失败:', fetchError);
      
      let errorMessage = 'API连接失败';
      if (fetchError.code === 'ECONNREFUSED') {
        errorMessage = '无法连接到API服务器';
      } else if (fetchError.code === 'ETIMEDOUT') {
        errorMessage = '连接超时';
      } else if (fetchError.message) {
        errorMessage = fetchError.message;
      }

      return NextResponse.json({ 
        success: false, 
        message: errorMessage 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('测试PX6 API连接失败:', error);
    return NextResponse.json({ error: '测试连接失败' }, { status: 500 });
  }
}
