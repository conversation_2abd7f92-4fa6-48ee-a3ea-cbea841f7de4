import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { verifyUserToken } from '@/lib/auth-demo';

// 验证管理员权限（演示版本）
function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供有效的认证token');
  }

  const token = authHeader.substring(7);
  const user = verifyUserToken(token);

  if (user.role !== 'admin') {
    throw new Error('需要管理员权限');
  }

  return user;
}

// 初始化数据库（演示版本 - 创建数据目录）
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    verifyAdmin(request);

    // 演示版本：创建数据目录
    const dataDir = path.join(process.cwd(), 'data');

    try {
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // 创建默认设置文件
      const settingsFile = path.join(dataDir, 'system_settings.json');
      if (!fs.existsSync(settingsFile)) {
        const defaultSettings = {
          px6_api_key: '',
          px6_api_url: 'https://api.px6.io',
          max_proxies_per_user: 10,
          default_proxy_duration: 30,
          enable_auto_renewal: true,
          maintenance_mode: false,
          system_notice: ''
        };
        fs.writeFileSync(settingsFile, JSON.stringify(defaultSettings, null, 2));
      }

      return NextResponse.json({
        message: '数据目录初始化成功',
        details: '已创建数据目录和默认设置文件'
      });
    } catch (error) {
      return NextResponse.json({
        error: '初始化失败',
        details: error
      }, { status: 500 });
    }
  } catch (error) {
    console.error('初始化数据库失败:', error);
    return NextResponse.json({ error: '初始化数据库失败' }, { status: 500 });
  }
}

// 检查数据状态（演示版本）
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    verifyAdmin(request);

    // 检查数据目录和文件
    const dataDir = path.join(process.cwd(), 'data');
    const settingsFile = path.join(dataDir, 'system_settings.json');

    const dirExists = fs.existsSync(dataDir);
    const fileExists = fs.existsSync(settingsFile);

    if (dirExists && fileExists) {
      return NextResponse.json({
        status: 'connected',
        message: '数据目录和设置文件正常'
      });
    } else {
      return NextResponse.json({
        status: 'disconnected',
        message: '数据目录或设置文件不存在'
      });
    }
  } catch (error) {
    console.error('检查数据状态失败:', error);
    return NextResponse.json({ error: '检查数据状态失败' }, { status: 500 });
  }
}
