import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { initializeDatabase, checkDatabaseConnection } from '@/lib/db-init';

// 初始化数据库
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份和管理员权限
    const user = requireAdmin(request);

    // 检查数据库连接
    const connectionCheck = await checkDatabaseConnection();
    if (!connectionCheck.success) {
      return NextResponse.json({ 
        error: '数据库连接失败', 
        details: connectionCheck.error 
      }, { status: 500 });
    }

    // 初始化数据库
    const result = await initializeDatabase();
    
    if (result.success) {
      return NextResponse.json({ 
        message: '数据库初始化成功',
        details: result.message 
      });
    } else {
      return NextResponse.json({ 
        error: '数据库初始化失败', 
        details: result.error 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('初始化数据库失败:', error);
    return NextResponse.json({ error: '初始化数据库失败' }, { status: 500 });
  }
}

// 检查数据库状态
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份和管理员权限
    const user = requireAdmin(request);

    // 检查数据库连接
    const result = await checkDatabaseConnection();
    
    if (result.success) {
      return NextResponse.json({ 
        status: 'connected',
        message: result.message 
      });
    } else {
      return NextResponse.json({ 
        status: 'disconnected',
        error: result.error 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('检查数据库状态失败:', error);
    return NextResponse.json({ error: '检查数据库状态失败' }, { status: 500 });
  }
}
