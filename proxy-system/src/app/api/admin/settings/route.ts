import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { verifyUserToken } from '@/lib/auth-demo';

// 使用文件存储系统设置（演示版本）
const SETTINGS_FILE = path.join(process.cwd(), 'data', 'system_settings.json');

// 确保数据目录存在
function ensureDataDir() {
  const dataDir = path.dirname(SETTINGS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// 读取设置
function readSettings() {
  ensureDataDir();
  try {
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('读取设置文件失败:', error);
  }
  
  // 返回默认设置
  return {
    px6_api_key: '',
    px6_api_url: 'https://api.px6.io',
    max_proxies_per_user: 10,
    default_proxy_duration: 30,
    enable_auto_renewal: true,
    maintenance_mode: false,
    system_notice: ''
  };
}

// 保存设置
function saveSettings(settings: any) {
  ensureDataDir();
  try {
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('保存设置文件失败:', error);
    return false;
  }
}

// 验证管理员权限（演示版本）
function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供有效的认证token');
  }

  const token = authHeader.substring(7);
  const user = verifyUserToken(token);

  if (user.role !== 'admin') {
    throw new Error('需要管理员权限');
  }

  return user;
}

// 获取系统设置
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    verifyAdmin(request);

    const settings = readSettings();
    return NextResponse.json(settings);
  } catch (error: any) {
    console.error('获取系统设置失败:', error);

    // 检查是否是认证错误
    if (error.message.includes('认证') || error.message.includes('权限')) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    return NextResponse.json({ error: '获取系统设置失败' }, { status: 500 });
  }
}

// 保存系统设置
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    verifyAdmin(request);

    const newSettings = await request.json();
    
    // 验证必要字段
    if (!newSettings.px6_api_url) {
      return NextResponse.json({ error: 'PX6 API地址不能为空' }, { status: 400 });
    }

    // 保存设置
    const success = saveSettings(newSettings);
    
    if (success) {
      return NextResponse.json({ message: '设置保存成功' });
    } else {
      return NextResponse.json({ error: '保存设置失败' }, { status: 500 });
    }
  } catch (error: any) {
    console.error('保存系统设置失败:', error);

    // 检查是否是认证错误
    if (error.message.includes('认证') || error.message.includes('权限')) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    return NextResponse.json({ error: '保存系统设置失败' }, { status: 500 });
  }
}
