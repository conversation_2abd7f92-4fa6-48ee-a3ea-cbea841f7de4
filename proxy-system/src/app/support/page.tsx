'use client';

import React from 'react';
import { 
  Layout, 
  Typography, 
  Card, 
  Row, 
  Col, 
  Button, 
  Form,
  Input,
  Select,
  message,
  Collapse,
  Space,
  Tag
} from 'antd';
import { 
  Globe,
  MessageCircle,
  Mail,
  Phone,
  Clock,
  HelpCircle,
  FileText,
  Users
} from 'lucide-react';
import Link from 'next/link';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

export default function SupportPage() {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Support form submitted:', values);
    message.success('您的问题已提交，我们会尽快回复您！');
    form.resetFields();
  };

  const faqData = [
    {
      key: '1',
      question: '如何开始使用代理服务？',
      answer: '注册账户后，选择适合的套餐，完成支付即可获得代理配置信息。我们提供详细的配置教程和技术支持。'
    },
    {
      key: '2',
      question: '代理服务支持哪些协议？',
      answer: '我们支持HTTP、HTTPS和SOCKS5协议，可以满足各种应用场景的需求。'
    },
    {
      key: '3',
      question: '如何配置代理？',
      answer: '购买后您会收到包含IP地址、端口、用户名和密码的配置信息。可以在浏览器、应用程序或代码中配置使用。'
    },
    {
      key: '4',
      question: '代理IP会变化吗？',
      answer: '独享代理的IP地址在服务期内保持不变。共享代理的IP可能会定期轮换以保证服务质量。'
    },
    {
      key: '5',
      question: '支持退款吗？',
      answer: '我们提供7天无理由退款保证。如果您对服务不满意，可以在购买后7天内申请全额退款。'
    },
    {
      key: '6',
      question: '如何查看使用统计？',
      answer: '登录用户面板即可查看详细的使用统计，包括流量使用、连接次数、在线时间等信息。'
    }
  ];

  return (
    <Layout className="min-h-screen">
      {/* 导航栏 */}
      <Header className="bg-white shadow-sm px-0" style={{ backgroundColor: 'white' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <Globe className="text-white" size={18} />
              </div>
              <Title level={3} className="!mb-0" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-gray-600 hover:text-gray-900">首页</Link>
              <Link href="/products" className="text-gray-600 hover:text-gray-900">产品服务</Link>
              <Link href="/pricing" className="text-gray-600 hover:text-gray-900">价格方案</Link>
              <Link href="/support" className="text-blue-600 font-medium">技术支持</Link>
              <Link href="/auth/login">
                <Button type="text">登录</Button>
              </Link>
              <Link href="/auth/register">
                <Button type="primary">注册</Button>
              </Link>
            </div>
          </div>
        </div>
      </Header>

      <Content>
        {/* 页面标题 */}
        <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={1} className="!text-4xl !font-bold !text-gray-900 !mb-4">
              技术支持
            </Title>
            <Paragraph className="!text-xl !text-gray-600 !max-w-3xl !mx-auto">
              我们提供全方位的技术支持服务，帮助您快速解决问题，
              确保代理服务的稳定运行。
            </Paragraph>
          </div>
        </section>

        {/* 支持方式 */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <Title level={2} className="!text-3xl !font-bold !text-gray-900">
                联系我们
              </Title>
              <Paragraph className="!text-lg !text-gray-600">
                选择最适合您的联系方式
              </Paragraph>
            </div>
            
            <Row gutter={[32, 32]}>
              <Col xs={24} md={8}>
                <Card className="text-center h-full shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <MessageCircle className="text-white" size={32} />
                  </div>
                  <Title level={4}>在线客服</Title>
                  <Paragraph className="text-gray-600 mb-6">
                    7×24小时在线客服，实时解答您的问题
                  </Paragraph>
                  <Button type="primary" size="large" block>
                    开始对话
                  </Button>
                  <div className="mt-4">
                    <Tag color="green">平均响应时间: 2分钟</Tag>
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} md={8}>
                <Card className="text-center h-full shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Mail className="text-white" size={32} />
                  </div>
                  <Title level={4}>邮件支持</Title>
                  <Paragraph className="text-gray-600 mb-6">
                    发送邮件给我们，获得详细的技术支持
                  </Paragraph>
                  <Button size="large" block>
                    发送邮件
                  </Button>
                  <div className="mt-4">
                    <Tag color="blue">平均响应时间: 4小时</Tag>
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} md={8}>
                <Card className="text-center h-full shadow-lg">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Phone className="text-white" size={32} />
                  </div>
                  <Title level={4}>电话支持</Title>
                  <Paragraph className="text-gray-600 mb-6">
                    企业用户专享电话技术支持服务
                  </Paragraph>
                  <Button size="large" block>
                    预约通话
                  </Button>
                  <div className="mt-4">
                    <Tag color="orange">仅限企业用户</Tag>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        </section>

        {/* 提交工单 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <Title level={2} className="!text-3xl !font-bold !text-gray-900">
                提交工单
              </Title>
              <Paragraph className="!text-lg !text-gray-600">
                详细描述您遇到的问题，我们会尽快为您解决
              </Paragraph>
            </div>
            
            <Card className="shadow-lg">
              <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                size="large"
              >
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="name"
                      label="姓名"
                      rules={[{ required: true, message: '请输入您的姓名' }]}
                    >
                      <Input placeholder="请输入您的姓名" />
                    </Form.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="email"
                      label="邮箱"
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' }
                      ]}
                    >
                      <Input placeholder="请输入您的邮箱" />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Form.Item
                  name="category"
                  label="问题类型"
                  rules={[{ required: true, message: '请选择问题类型' }]}
                >
                  <Select placeholder="请选择问题类型">
                    <Option value="technical">技术问题</Option>
                    <Option value="billing">账单问题</Option>
                    <Option value="account">账户问题</Option>
                    <Option value="feature">功能建议</Option>
                    <Option value="other">其他问题</Option>
                  </Select>
                </Form.Item>
                
                <Form.Item
                  name="subject"
                  label="问题标题"
                  rules={[{ required: true, message: '请输入问题标题' }]}
                >
                  <Input placeholder="简要描述您的问题" />
                </Form.Item>
                
                <Form.Item
                  name="description"
                  label="详细描述"
                  rules={[{ required: true, message: '请详细描述您的问题' }]}
                >
                  <TextArea 
                    rows={6} 
                    placeholder="请详细描述您遇到的问题，包括错误信息、操作步骤等"
                  />
                </Form.Item>
                
                <Form.Item>
                  <Button type="primary" htmlType="submit" size="large" block>
                    提交工单
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </div>
        </section>

        {/* 常见问题 */}
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <Title level={2} className="!text-3xl !font-bold !text-gray-900">
                常见问题
              </Title>
              <Paragraph className="!text-lg !text-gray-600">
                查看常见问题解答，快速找到解决方案
              </Paragraph>
            </div>
            
            <Collapse size="large" ghost>
              {faqData.map(item => (
                <Panel 
                  header={
                    <div className="flex items-center">
                      <HelpCircle size={20} className="text-blue-600 mr-3" />
                      <Text strong>{item.question}</Text>
                    </div>
                  } 
                  key={item.key}
                >
                  <Paragraph className="text-gray-600 ml-8">
                    {item.answer}
                  </Paragraph>
                </Panel>
              ))}
            </Collapse>
          </div>
        </section>

        {/* 服务时间 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={2} className="!text-3xl !font-bold !text-gray-900 !mb-8">
              服务时间
            </Title>
            
            <Row gutter={[32, 32]}>
              <Col xs={24} md={8}>
                <Card className="text-center">
                  <Clock className="text-blue-600 mx-auto mb-4" size={32} />
                  <Title level={4}>在线客服</Title>
                  <Text>7×24小时</Text>
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card className="text-center">
                  <Mail className="text-green-600 mx-auto mb-4" size={32} />
                  <Title level={4}>邮件支持</Title>
                  <Text>工作日 9:00-18:00</Text>
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card className="text-center">
                  <Phone className="text-purple-600 mx-auto mb-4" size={32} />
                  <Title level={4}>电话支持</Title>
                  <Text>工作日 9:00-18:00</Text>
                </Card>
              </Col>
            </Row>
          </div>
        </section>
      </Content>

      {/* 简化的页脚 */}
      <Footer className="bg-gray-900 text-center py-8" style={{ backgroundColor: '#111827' }}>
        <Text style={{ color: '#9ca3af' }}>
          © 2024 ProxyHub. 保留所有权利.
        </Text>
      </Footer>
    </Layout>
  );
}
