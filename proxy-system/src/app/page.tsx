'use client';

import React, { useEffect, useState } from 'react';
import {
  Button,
  Typography,
  Card,
  Row,
  Col,
  Space,
  Statistic,
  Timeline,
  Divider,
  Badge,
  Spin,
  Layout
} from 'antd';
import {
  UserOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import {
  Globe,
  Zap,
  Shield,
  CheckCircle,
  Star,
  Users,
  Clock,
  Lock,
  Rocket,
  ShoppingCart,
  TrendingUp,
  Award,
  Target,
  Wifi,
  Server,
  Database,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import LoadingScreen from '@/components/LoadingScreen';
import AppHeader from '@/components/AppHeader';
import AppFooter from '@/components/AppFooter';

const { Title, Paragraph, Text } = Typography;
const { Header, Content, Footer } = Layout;

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // 如果用户已登录，重定向到仪表板
    if (user && !loading) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  // 优化加载状态显示
  if (!mounted) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div style={{ fontSize: '18px', color: '#1890ff' }}>
          ProxyHub
        </div>
        <div style={{ fontSize: '14px', color: '#666' }}>
          正在加载页面内容...
        </div>
      </div>
    );
  }

  if (loading) {
    return <LoadingScreen message="正在加载页面内容..." />;
  }

  return (
    <Layout className="min-h-screen">
      <AppHeader currentPage="home" />

      <Content>
        {/* 英雄区域 */}
        <section className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <Badge.Ribbon text="企业级服务" color="blue">
                <Title level={1} className="!text-5xl !font-bold !text-gray-900 !mb-6">
                  全球领先的代理服务平台
                </Title>
              </Badge.Ribbon>
              <Paragraph className="!text-xl !text-gray-600 !mt-6 !max-w-4xl !mx-auto !leading-relaxed">
                基于PX6.me的企业级代理服务，提供IPv4、IPv6和共享代理解决方案。
                支持HTTP/HTTPS和SOCKS5协议，覆盖全球50+个国家和地区，
                为您的业务提供稳定、安全、高速的网络代理服务。
              </Paragraph>

              <div className="mt-10">
                <Space size="large">
                  <Link href="/auth/register">
                    <Button
                      type="primary"
                      size="large"
                      icon={<Rocket size={20} />}
                      className="h-12 px-8 text-lg bg-gradient-to-r from-blue-500 to-indigo-600 border-0 shadow-lg hover:shadow-xl"
                    >
                      立即开始免费试用
                    </Button>
                  </Link>
                  <Link href="/auth/login">
                    <Button size="large" className="h-12 px-8 text-lg">
                      已有账户登录
                    </Button>
                  </Link>
                </Space>
              </div>

              {/* 统计数据 */}
              <div className="mt-16">
                <Row gutter={[32, 16]} justify="center">
                  <Col xs={12} sm={6}>
                    <Statistic
                      title="服务国家"
                      value={50}
                      suffix="+"
                      valueStyle={{ color: '#1890ff', fontSize: '2rem', fontWeight: 'bold' }}
                    />
                  </Col>
                  <Col xs={12} sm={6}>
                    <Statistic
                      title="在线代理"
                      value={100000}
                      suffix="+"
                      valueStyle={{ color: '#52c41a', fontSize: '2rem', fontWeight: 'bold' }}
                    />
                  </Col>
                  <Col xs={12} sm={6}>
                    <Statistic
                      title="企业客户"
                      value={5000}
                      suffix="+"
                      valueStyle={{ color: '#722ed1', fontSize: '2rem', fontWeight: 'bold' }}
                    />
                  </Col>
                  <Col xs={12} sm={6}>
                    <Statistic
                      title="服务可用性"
                      value={99.9}
                      suffix="%"
                      valueStyle={{ color: '#fa8c16', fontSize: '2rem', fontWeight: 'bold' }}
                    />
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </section>

        {/* 核心优势 */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Title level={2} className="!text-4xl !font-bold !text-gray-900">
                为什么选择我们
              </Title>
              <Paragraph className="!text-lg !text-gray-600 !mt-4">
                专业的技术团队，企业级的服务标准，为您提供最优质的代理解决方案
              </Paragraph>
            </div>

            <Row gutter={[48, 48]}>
              <Col xs={24} md={8}>
                <Card
                  className="text-center h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Globe className="text-white" size={32} />
                  </div>
                  <Title level={4} className="!mb-4">全球网络覆盖</Title>
                  <Paragraph className="!text-gray-600 !leading-relaxed">
                    覆盖全球50+个国家和地区，包括美国、欧洲、亚洲等主要市场。
                    多个数据中心分布，确保就近接入，降低延迟。
                  </Paragraph>
                  <div className="mt-4">
                    <Space wrap>
                      <Badge color="blue" text="美国" />
                      <Badge color="green" text="欧洲" />
                      <Badge color="orange" text="亚洲" />
                      <Badge color="purple" text="其他" />
                    </Space>
                  </div>
                </Card>
              </Col>

              <Col xs={24} md={8}>
                <Card
                  className="text-center h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Zap className="text-white" size={32} />
                  </div>
                  <Title level={4} className="!mb-4">高速稳定连接</Title>
                  <Paragraph className="!text-gray-600 !leading-relaxed">
                    基于PX6.me的优质线路，提供高速稳定的代理服务。
                    99.9%的可用性保证，24/7不间断服务。
                  </Paragraph>
                  <div className="mt-4">
                    <Space direction="vertical" size="small">
                      <Text><CheckCircle className="text-green-500 mr-2" size={16} />99.9% 服务可用性</Text>
                      <Text><CheckCircle className="text-green-500 mr-2" size={16} />24/7 技术支持</Text>
                      <Text><CheckCircle className="text-green-500 mr-2" size={16} />秒级响应时间</Text>
                    </Space>
                  </div>
                </Card>
              </Col>

              <Col xs={24} md={8}>
                <Card
                  className="text-center h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Shield className="text-white" size={32} />
                  </div>
                  <Title level={4} className="!mb-4">企业级安全</Title>
                  <Paragraph className="!text-gray-600 !leading-relaxed">
                    支持HTTP/HTTPS和SOCKS5协议，提供端到端加密传输。
                    严格的数据保护政策，保障您的业务安全。
                  </Paragraph>
                  <div className="mt-4">
                    <Space direction="vertical" size="small">
                      <Text><Lock className="text-blue-500 mr-2" size={16} />SSL/TLS 加密</Text>
                      <Text><Lock className="text-blue-500 mr-2" size={16} />数据隐私保护</Text>
                      <Text><Lock className="text-blue-500 mr-2" size={16} />合规认证</Text>
                    </Space>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        </section>

        {/* 服务类型 */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Title level={2} className="!text-4xl !font-bold !text-gray-900">
                多样化的代理服务
              </Title>
              <Paragraph className="!text-lg !text-gray-600 !mt-4">
                根据不同业务需求，提供多种类型的代理服务解决方案
              </Paragraph>
            </div>

            <Row gutter={[32, 32]}>
              <Col xs={24} lg={8}>
                <Card
                  className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <Award className="text-blue-600" size={24} />
                    </div>
                    <Title level={4} className="!mb-0">IPv4 独享代理</Title>
                  </div>
                  <Paragraph className="!text-gray-600 !mb-6">
                    独享IPv4代理，提供最佳的兼容性和稳定性，适合各种业务场景。
                    每个IP地址仅供单一用户使用，确保最优性能。
                  </Paragraph>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>独享IP地址，无共享风险</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>高速稳定，低延迟</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>支持HTTP/HTTPS和SOCKS5</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>适合企业级应用</Text>
                    </div>
                  </div>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <Text className="text-blue-600 font-semibold">推荐用途：</Text>
                    <div className="mt-2">
                      <Space wrap>
                        <Badge color="blue" text="数据采集" />
                        <Badge color="green" text="SEO监控" />
                        <Badge color="orange" text="广告验证" />
                      </Space>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col xs={24} lg={8}>
                <Card
                  className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                      <Wifi className="text-green-600" size={24} />
                    </div>
                    <Title level={4} className="!mb-0">IPv6 代理</Title>
                  </div>
                  <Paragraph className="!text-gray-600 !mb-6">
                    下一代互联网协议，提供更大的地址空间和更好的性能。
                    价格更优惠，是未来网络发展的趋势。
                  </Paragraph>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>海量IP资源池</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>价格更加优惠</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>未来网络标准</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>更好的路由性能</Text>
                    </div>
                  </div>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <Text className="text-green-600 font-semibold">推荐用途：</Text>
                    <div className="mt-2">
                      <Space wrap>
                        <Badge color="green" text="批量操作" />
                        <Badge color="blue" text="内容分发" />
                        <Badge color="purple" text="负载均衡" />
                      </Space>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col xs={24} lg={8}>
                <Card
                  className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  bodyStyle={{ padding: '2rem' }}
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                      <Users className="text-orange-600" size={24} />
                    </div>
                    <Title level={4} className="!mb-0">共享代理</Title>
                  </div>
                  <Paragraph className="!text-gray-600 !mb-6">
                    经济实惠的共享IPv4代理，适合预算有限的用户。
                    虽然是共享使用，但同样提供稳定可靠的服务。
                  </Paragraph>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>价格经济实惠</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>基础功能完整</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>适合入门用户</Text>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <Text>快速部署使用</Text>
                    </div>
                  </div>
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <Text className="text-orange-600 font-semibold">推荐用途：</Text>
                    <div className="mt-2">
                      <Space wrap>
                        <Badge color="orange" text="个人使用" />
                        <Badge color="blue" text="学习测试" />
                        <Badge color="green" text="小型项目" />
                      </Space>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        </section>

        {/* 使用流程 */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Title level={2} className="!text-4xl !font-bold !text-gray-900">
                简单三步，即刻开始
              </Title>
              <Paragraph className="!text-lg !text-gray-600 !mt-4">
                快速注册，选择套餐，立即使用 - 让代理服务变得简单高效
              </Paragraph>
            </div>

            <Row gutter={[48, 32]} justify="center">
              <Col xs={24} md={8}>
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <UserOutlined className="text-white text-3xl" />
                  </div>
                  <Title level={4} className="!mb-4">1. 注册账户</Title>
                  <Paragraph className="!text-gray-600">
                    快速注册账户，验证邮箱即可开始使用。
                    新用户享受免费试用额度。
                  </Paragraph>
                </div>
              </Col>

              <Col xs={24} md={8}>
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <ShoppingCart className="text-white" size={36} />
                  </div>
                  <Title level={4} className="!mb-4">2. 选择套餐</Title>
                  <Paragraph className="!text-gray-600">
                    根据业务需求选择合适的代理套餐，
                    支持按需购买，灵活配置。
                  </Paragraph>
                </div>
              </Col>

              <Col xs={24} md={8}>
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Rocket className="text-white" size={36} />
                  </div>
                  <Title level={4} className="!mb-4">3. 立即使用</Title>
                  <Paragraph className="!text-gray-600">
                    获取代理配置信息，集成到您的应用中，
                    开始享受高质量的代理服务。
                  </Paragraph>
                </div>
              </Col>
            </Row>
          </div>
        </section>

        {/* 客户案例和时间线 */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Row gutter={[48, 48]}>
              <Col xs={24} lg={12}>
                <Title level={2} className="!text-3xl !font-bold !text-gray-900 !mb-8">
                  发展历程
                </Title>
                <Timeline
                  items={[
                    {
                      color: 'blue',
                      children: (
                        <div>
                          <Title level={5} className="!mb-2">2024年 - 平台上线</Title>
                          <Text className="text-gray-600">
                            正式推出代理服务平台，集成PX6.me优质资源，
                            为用户提供稳定可靠的代理服务。
                          </Text>
                        </div>
                      ),
                    },
                    {
                      color: 'green',
                      children: (
                        <div>
                          <Title level={5} className="!mb-2">服务优化</Title>
                          <Text className="text-gray-600">
                            持续优化服务质量，扩展全球节点覆盖，
                            提升用户体验和服务稳定性。
                          </Text>
                        </div>
                      ),
                    },
                    {
                      color: 'orange',
                      children: (
                        <div>
                          <Title level={5} className="!mb-2">企业级功能</Title>
                          <Text className="text-gray-600">
                            推出企业级功能，包括API接口、批量管理、
                            自定义配置等高级特性。
                          </Text>
                        </div>
                      ),
                    },
                    {
                      color: 'purple',
                      children: (
                        <div>
                          <Title level={5} className="!mb-2">未来规划</Title>
                          <Text className="text-gray-600">
                            计划推出更多创新功能，包括智能路由、
                            AI优化、更多地区覆盖等。
                          </Text>
                        </div>
                      ),
                    },
                  ]}
                />
              </Col>

              <Col xs={24} lg={12}>
                <Title level={2} className="!text-3xl !font-bold !text-gray-900 !mb-8">
                  客户评价
                </Title>
                <div className="space-y-6">
                  <Card className="border-l-4 border-l-blue-500 shadow-md">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <UserOutlined className="text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Text strong>张先生</Text>
                          <Text className="text-gray-500">- 电商企业</Text>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star key={i} className="text-yellow-400 fill-current" size={16} />
                            ))}
                          </div>
                        </div>
                        <Text className="text-gray-600">
                          "服务非常稳定，客服响应及时，价格也很合理。
                          我们的数据采集业务运行得很顺畅。"
                        </Text>
                      </div>
                    </div>
                  </Card>

                  <Card className="border-l-4 border-l-green-500 shadow-md">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <UserOutlined className="text-green-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Text strong>李女士</Text>
                          <Text className="text-gray-500">- 营销公司</Text>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star key={i} className="text-yellow-400 fill-current" size={16} />
                            ))}
                          </div>
                        </div>
                        <Text className="text-gray-600">
                          "代理质量很高，速度快，稳定性好。
                          对我们的广告投放监控帮助很大。"
                        </Text>
                      </div>
                    </div>
                  </Card>

                  <Card className="border-l-4 border-l-purple-500 shadow-md">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <UserOutlined className="text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Text strong>王总</Text>
                          <Text className="text-gray-500">- 科技公司</Text>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star key={i} className="text-yellow-400 fill-current" size={16} />
                            ))}
                          </div>
                        </div>
                        <Text className="text-gray-600">
                          "技术支持很专业，帮我们解决了很多技术问题。
                          是值得信赖的合作伙伴。"
                        </Text>
                      </div>
                    </div>
                  </Card>
                </div>
              </Col>
            </Row>
          </div>
        </section>

        {/* CTA区域 */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-700">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={2} className="!text-4xl !font-bold !text-white !mb-6">
              准备开始您的代理服务之旅？
            </Title>
            <Paragraph className="!text-xl !text-blue-100 !mb-10">
              立即注册，享受免费试用额度，体验企业级代理服务
            </Paragraph>
            <Space size="large">
              <Link href="/auth/register">
                <Button
                  type="primary"
                  size="large"
                  icon={<Rocket size={20} />}
                  className="h-14 px-10 text-lg bg-white text-blue-600 border-0 hover:bg-gray-100"
                >
                  免费开始试用
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button
                  size="large"
                  className="h-14 px-10 text-lg text-white border-white hover:bg-white hover:text-blue-600"
                >
                  联系销售团队
                </Button>
              </Link>
            </Space>
          </div>
        </section>
      </Content>

      <AppFooter />
    </Layout>
  );
}
