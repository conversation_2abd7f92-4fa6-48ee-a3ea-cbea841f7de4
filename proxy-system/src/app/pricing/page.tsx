'use client';

import React, { useState } from 'react';
import { 
  Layout, 
  Typography, 
  Card, 
  Row, 
  Col, 
  Button, 
  Switch,
  Space,
  Tag,
  Divider
} from 'antd';
import { 
  Globe,
  CheckCircle,
  Star,
  Zap,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import AppHeader from '@/components/AppHeader';
import AppFooter from '@/components/AppFooter';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function PricingPage() {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: '基础版',
      description: '适合个人用户和小型项目',
      icon: Globe,
      color: 'from-blue-500 to-indigo-600',
      monthlyPrice: 99,
      yearlyPrice: 999,
      features: [
        '10个IPv4代理',
        '基础技术支持',
        '99% 可用性保证',
        'HTTP/SOCKS5 协议',
        '标准速度'
      ],
      popular: false
    },
    {
      name: '专业版',
      description: '适合中小企业和专业用户',
      icon: Zap,
      color: 'from-purple-500 to-pink-600',
      monthlyPrice: 299,
      yearlyPrice: 2999,
      features: [
        '50个IPv4代理',
        '优先技术支持',
        '99.9% 可用性保证',
        'HTTP/SOCKS5 协议',
        '高速连接',
        'IPv6 代理支持',
        '自定义配置'
      ],
      popular: true
    },
    {
      name: '企业版',
      description: '适合大型企业和高需求用户',
      icon: Shield,
      color: 'from-green-500 to-emerald-600',
      monthlyPrice: 999,
      yearlyPrice: 9999,
      features: [
        '200个IPv4代理',
        '24/7 专属支持',
        '99.99% 可用性保证',
        'HTTP/SOCKS5 协议',
        '极速连接',
        'IPv6 代理支持',
        '完全自定义',
        'API 接口',
        '专属客户经理'
      ],
      popular: false
    }
  ];

  return (
    <Layout className="min-h-screen">
      <AppHeader currentPage="pricing" />

      <Content>
        {/* 页面标题 */}
        <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={1} className="!text-4xl !font-bold !text-gray-900 !mb-4">
              价格方案
            </Title>
            <Paragraph className="!text-xl !text-gray-600 !max-w-3xl !mx-auto !mb-8">
              选择最适合您需求的代理服务方案，所有方案都包含核心功能，
              无隐藏费用，随时可以升级或降级。
            </Paragraph>
            
            {/* 计费周期切换 */}
            <div className="flex items-center justify-center space-x-4">
              <Text className={!isYearly ? 'font-medium text-blue-600' : 'text-gray-600'}>月付</Text>
              <Switch 
                checked={isYearly} 
                onChange={setIsYearly}
                size="large"
              />
              <Text className={isYearly ? 'font-medium text-blue-600' : 'text-gray-600'}>年付</Text>
              <Tag color="green">年付享8.5折</Tag>
            </div>
          </div>
        </section>

        {/* 价格卡片 */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Row gutter={[32, 32]} justify="center">
              {plans.map((plan, index) => {
                const PlanIcon = plan.icon;
                const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice;
                const originalYearlyPrice = plan.monthlyPrice * 12;
                const savings = originalYearlyPrice - plan.yearlyPrice;
                
                return (
                  <Col xs={24} lg={8} key={index}>
                    <Card 
                      className={`h-full text-center relative ${
                        plan.popular ? 'border-2 border-blue-500 shadow-2xl' : 'shadow-lg'
                      }`}
                      bodyStyle={{ padding: '2rem' }}
                    >
                      {plan.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <Tag color="blue" className="px-4 py-1 text-sm font-medium">
                            <Star size={14} className="inline mr-1" />
                            最受欢迎
                          </Tag>
                        </div>
                      )}
                      
                      <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mx-auto mb-6`}>
                        <PlanIcon className="text-white" size={32} />
                      </div>
                      
                      <Title level={3} className="!mb-2">{plan.name}</Title>
                      <Paragraph className="text-gray-600 !mb-6">{plan.description}</Paragraph>
                      
                      <div className="mb-6">
                        <div className="flex items-baseline justify-center">
                          <span className="text-4xl font-bold text-gray-900">¥{price}</span>
                          <span className="text-gray-600 ml-2">/{isYearly ? '年' : '月'}</span>
                        </div>
                        {isYearly && (
                          <div className="mt-2">
                            <Text className="text-sm text-gray-500 line-through">¥{originalYearlyPrice}/年</Text>
                            <Text className="text-sm text-green-600 ml-2">节省 ¥{savings}</Text>
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-4 mb-8">
                        {plan.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center">
                            <CheckCircle size={16} className="text-green-500 mr-3 flex-shrink-0" />
                            <Text>{feature}</Text>
                          </div>
                        ))}
                      </div>
                      
                      <Link href="/auth/register">
                        <Button 
                          type={plan.popular ? "primary" : "default"}
                          size="large"
                          block
                          className={plan.popular ? "h-12 bg-gradient-to-r from-blue-500 to-indigo-600 border-0" : "h-12"}
                        >
                          {plan.popular ? "立即开始" : "选择方案"}
                        </Button>
                      </Link>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          </div>
        </section>

        {/* 常见问题 */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <Title level={2} className="!text-3xl !font-bold !text-gray-900">
                常见问题
              </Title>
            </div>
            
            <div className="space-y-8">
              <Card>
                <Title level={4}>可以随时升级或降级方案吗？</Title>
                <Paragraph>
                  是的，您可以随时升级或降级您的方案。升级会立即生效，降级会在下个计费周期生效。
                  我们会按比例计算费用差额。
                </Paragraph>
              </Card>
              
              <Card>
                <Title level={4}>支持哪些支付方式？</Title>
                <Paragraph>
                  我们支持支付宝、微信支付、银行卡等多种支付方式。企业用户还可以申请月结账单。
                </Paragraph>
              </Card>
              
              <Card>
                <Title level={4}>有免费试用吗？</Title>
                <Paragraph>
                  新用户注册即可获得免费试用额度，可以体验我们的服务质量。
                  试用期间享受完整功能，无任何限制。
                </Paragraph>
              </Card>
              
              <Card>
                <Title level={4}>如何获得技术支持？</Title>
                <Paragraph>
                  我们提供多种技术支持方式：在线客服、工单系统、邮件支持。
                  专业版和企业版用户享受优先支持服务。
                </Paragraph>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA区域 */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Title level={2} className="!text-3xl !font-bold !text-white !mb-4">
              还有疑问？
            </Title>
            <Paragraph className="!text-xl !text-blue-100 !mb-8">
              联系我们的销售团队，获得个性化的方案建议
            </Paragraph>
            <Space size="large">
              <Link href="/support">
                <Button 
                  type="primary" 
                  size="large" 
                  className="h-12 px-8 text-lg bg-white text-blue-600 border-0 hover:bg-gray-100"
                >
                  联系销售
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button 
                  size="large" 
                  className="h-12 px-8 text-lg text-white border-white hover:bg-white hover:text-blue-600"
                >
                  免费试用
                </Button>
              </Link>
            </Space>
          </div>
        </section>
      </Content>

      <AppFooter />
    </Layout>
  );
}
