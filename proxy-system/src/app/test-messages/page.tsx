'use client';

import React from 'react';
import { Button, Card, Space, Typography, message } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

export default function TestMessagesPage() {
  // 测试基础JavaScript功能
  const testAlert = () => {
    alert('✅ JavaScript正常工作！这说明页面交互没有问题。');
  };
  const testSuccess = () => {
    console.log('🔍 测试成功消息被点击');
    try {
      const result = message.success({
        content: '✅ 这是一个成功消息！',
        duration: 3,
        style: { marginTop: '20vh' }
      });
      console.log('✅ 成功消息调用结果:', result);
    } catch (error) {
      console.error('❌ 成功消息调用失败:', error);
      alert('成功消息调用失败: ' + error);
    }
  };

  const testError = () => {
    console.log('🔍 测试错误消息被点击');
    try {
      const result = message.error({
        content: '❌ 这是一个错误消息！',
        duration: 4,
        style: { marginTop: '20vh' }
      });
      console.log('✅ 错误消息调用结果:', result);
    } catch (error) {
      console.error('❌ 错误消息调用失败:', error);
      alert('错误消息调用失败: ' + error);
    }
  };

  const testWarning = () => {
    message.warning({
      content: '⚠️ 这是一个警告消息！',
      duration: 3,
      style: { marginTop: '20vh' }
    });
  };

  const testInfo = () => {
    message.info({
      content: 'ℹ️ 这是一个信息消息！',
      duration: 3,
      style: { marginTop: '20vh' }
    });
  };

  const testLoading = () => {
    const hide = message.loading({
      content: '正在加载中...',
      duration: 0,
      style: { marginTop: '20vh' }
    });

    setTimeout(() => {
      hide();
      message.success({
        content: '✅ 加载完成！',
        duration: 2,
        style: { marginTop: '20vh' }
      });
    }, 2000);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>消息提示测试页面</Title>
        <Text type="secondary">
          这个页面用于测试各种消息提示是否正常工作。
        </Text>

        <div style={{ marginTop: '24px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Title level={4}>基础功能测试</Title>
              <Space wrap>
                <Button
                  type="default"
                  onClick={testAlert}
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: 'white' }}
                >
                  测试JavaScript
                </Button>
              </Space>
            </div>

            <div>
              <Title level={4}>基础消息类型</Title>
              <Space wrap>
                <Button 
                  type="primary" 
                  icon={<CheckCircleOutlined />}
                  onClick={testSuccess}
                >
                  成功消息
                </Button>
                <Button 
                  danger 
                  icon={<CloseCircleOutlined />}
                  onClick={testError}
                >
                  错误消息
                </Button>
                <Button 
                  style={{ backgroundColor: '#faad14', borderColor: '#faad14', color: 'white' }}
                  icon={<ExclamationCircleOutlined />}
                  onClick={testWarning}
                >
                  警告消息
                </Button>
                <Button 
                  icon={<InfoCircleOutlined />}
                  onClick={testInfo}
                >
                  信息消息
                </Button>
              </Space>
            </div>

            <div>
              <Title level={4}>加载消息</Title>
              <Button 
                type="primary" 
                onClick={testLoading}
                style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
              >
                测试加载消息
              </Button>
            </div>

            <div>
              <Title level={4}>使用说明</Title>
              <ul>
                <li>所有消息都会显示在屏幕中上方（marginTop: 20vh）</li>
                <li>成功消息显示3秒</li>
                <li>错误消息显示4秒</li>
                <li>警告和信息消息显示3秒</li>
                <li>加载消息不会自动消失，需要手动关闭</li>
              </ul>
            </div>
          </Space>
        </div>
      </Card>
    </div>
  );
}
