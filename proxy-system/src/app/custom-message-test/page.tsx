'use client';

import React from 'react';
import CustomMessage, { useCustomMessage, customMessage } from '@/components/CustomMessage';

export default function CustomMessageTestPage() {
  const { messages, onRemove } = useCustomMessage();

  const testSuccess = () => {
    console.log('🔍 测试自定义成功消息');
    customMessage.success('✅ 这是一个自定义成功消息！');
  };

  const testError = () => {
    console.log('🔍 测试自定义错误消息');
    customMessage.error('❌ 这是一个自定义错误消息！');
  };

  const testWarning = () => {
    console.log('🔍 测试自定义警告消息');
    customMessage.warning('⚠️ 这是一个自定义警告消息！');
  };

  const testInfo = () => {
    console.log('🔍 测试自定义信息消息');
    customMessage.info('ℹ️ 这是一个自定义信息消息！');
  };

  const testLoading = () => {
    console.log('🔍 测试自定义加载消息');
    const hide = customMessage.loading('🔄 正在加载中...');
    
    setTimeout(() => {
      hide();
      customMessage.success('✅ 加载完成！');
    }, 3000);
  };

  const testAlert = () => {
    alert('✅ JavaScript正常工作！');
  };

  return (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <h1>自定义消息系统测试页面</h1>
      <p>这个页面使用自定义的消息系统，不依赖Ant Design</p>
      
      <div style={{ marginTop: '30px' }}>
        <button 
          onClick={testAlert}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          测试JavaScript
        </button>
        
        <button 
          onClick={testSuccess}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          成功消息
        </button>
        
        <button 
          onClick={testError}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          错误消息
        </button>
        
        <button 
          onClick={testWarning}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#faad14',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          警告消息
        </button>
        
        <button 
          onClick={testInfo}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          信息消息
        </button>
        
        <button 
          onClick={testLoading}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#722ed1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          加载消息
        </button>
      </div>
      
      <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
        <p>当前显示的消息数量: {messages.length}</p>
        <p>这个消息系统完全独立于Ant Design，应该能正常工作</p>
      </div>

      {/* 渲染自定义消息 */}
      <CustomMessage messages={messages} onRemove={onRemove} />
    </div>
  );
}
