'use client';

import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { Globe, Shield, CheckCircle, Zap } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import LoadingScreen from '@/components/LoadingScreen';

const { Title, Text, Paragraph } = Typography;

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const { login, loading: authLoading } = useAuth();
  const router = useRouter();

  if (authLoading) {
    return <LoadingScreen message="正在验证用户状态..." />;
  }

  const onFinish = async (values: { email: string; password: string }) => {
    setLoading(true);
    try {
      await login(values.email, values.password);
      message.success('登录成功');
      router.push('/dashboard');
    } catch (error) {
      message.error(error instanceof Error ? error.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-indigo-200 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* 左侧信息区域 */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 p-12 flex-col justify-center">
          <div className="max-w-md">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <Globe className="text-white" size={28} />
              </div>
              <Title level={2} style={{ color: 'white', margin: 0 }}>ProxyHub</Title>
            </div>

            <Title level={3} style={{ color: 'white', marginBottom: '1.5rem' }}>
              欢迎回来！
            </Title>

            <Paragraph style={{ color: '#dbeafe', fontSize: '18px', lineHeight: '1.6', marginBottom: '2rem' }}>
              登录您的账户，继续享受专业的代理服务。我们为您提供稳定、安全、高速的网络代理解决方案。
            </Paragraph>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Shield className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>企业级安全保障</Text>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Globe className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>全球50+国家覆盖</Text>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Zap className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>7×24小时技术支持</Text>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧登录表单 */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="max-w-md w-full space-y-8">
            {/* 移动端品牌信息 */}
            <div className="text-center lg:hidden">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Globe className="text-white" size={24} />
                </div>
                <Title level={2} style={{ margin: 0, color: '#1f2937' }}>ProxyHub</Title>
              </div>
              <Text style={{ color: '#6b7280', fontSize: '16px' }}>专业的代理服务平台</Text>
            </div>

            <div className="text-center">
              <Title level={2} style={{ marginBottom: '0.5rem', color: '#1f2937' }}>登录账户</Title>
              <Text style={{ color: '#6b7280', fontSize: '16px' }}>输入您的凭据以访问您的账户</Text>
            </div>

            <Card className="shadow-2xl border-0" bodyStyle={{ padding: '2rem' }}>
              <Form
                name="login"
                onFinish={onFinish}
                autoComplete="off"
                layout="vertical"
                size="large"
              >
                <Form.Item
                  name="email"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>邮箱地址</span>}
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' }
                  ]}
                >
                  <Input
                    prefix={<MailOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="请输入您的邮箱地址"
                    className="h-12 rounded-lg"
                    style={{ fontSize: '16px' }}
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>密码</span>}
                  rules={[{ required: true, message: '请输入密码' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined style={{ color: '#9ca3af' }} />}
                    placeholder="请输入您的密码"
                    className="h-12 rounded-lg"
                    style={{ fontSize: '16px' }}
                    iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

                <div className="flex items-center justify-between mb-6">
                  <Form.Item name="remember" valuePropName="checked" className="!mb-0">
                    <Checkbox style={{ color: '#374151' }}>
                      <span style={{ color: '#374151', fontSize: '14px' }}>记住我</span>
                    </Checkbox>
                  </Form.Item>
                  <Link href="/auth/forgot-password" style={{ color: '#3b82f6', fontSize: '14px' }} className="hover:text-blue-500">
                    忘记密码？
                  </Link>
                </div>

                <Form.Item className="!mb-4">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className="h-12 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    style={{
                      fontSize: '16px',
                      fontWeight: 500,
                      background: 'linear-gradient(to right, #3b82f6, #4f46e5)',
                      border: 'none'
                    }}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Form.Item>
              </Form>

              <Divider style={{ margin: '1.5rem 0' }}>
                <Text style={{ color: '#9ca3af', fontSize: '14px' }}>或者</Text>
              </Divider>

              <div className="text-center">
                <Text style={{ color: '#6b7280', fontSize: '14px' }}>
                  还没有账户？{' '}
                  <Link href="/auth/register" style={{ color: '#3b82f6', fontWeight: 500 }} className="hover:text-blue-500">
                    立即注册
                  </Link>
                </Text>
              </div>
            </Card>

            {/* 底部链接 */}
            <div className="text-center space-x-6">
              <Link href="/privacy" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">隐私政策</Link>
              <Link href="/terms" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">服务条款</Link>
              <Link href="/support" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">技术支持</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
