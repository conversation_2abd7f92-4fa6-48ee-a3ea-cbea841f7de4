'use client';

import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider, Checkbox, Progress } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { Globe, Shield, CheckCircle, Gift, Zap } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import LoadingScreen from '@/components/LoadingScreen';

const { Title, Text, Paragraph } = Typography;

export default function RegisterPage() {
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const { register, loading: authLoading } = useAuth();
  const router = useRouter();

  if (authLoading) {
    return <LoadingScreen message="正在验证用户状态..." />;
  }

  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/\d/.test(password)) strength += 25;
    setPasswordStrength(strength);
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 50) return '#ff4d4f';
    if (passwordStrength < 75) return '#faad14';
    return '#52c41a';
  };

  const getPasswordStrengthText = () => {
    if (passwordStrength < 25) return '弱';
    if (passwordStrength < 50) return '一般';
    if (passwordStrength < 75) return '良好';
    return '强';
  };

  const onFinish = async (values: { email: string; username: string; password: string; confirmPassword: string; agreement: boolean }) => {
    if (!values.agreement) {
      message.error('请同意服务条款和隐私政策');
      return;
    }

    setLoading(true);
    try {
      await register(values.email, values.username, values.password);
      message.success('注册成功');
      router.push('/dashboard');
    } catch (error) {
      message.error(error instanceof Error ? error.message : '注册失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-indigo-200 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* 左侧信息区域 */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 p-12 flex-col justify-center">
          <div className="max-w-md">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <Globe className="text-white" size={28} />
              </div>
              <Title level={2} style={{ color: 'white', margin: 0 }}>ProxyHub</Title>
            </div>

            <Title level={3} style={{ color: 'white', marginBottom: '1.5rem' }}>
              开始您的代理之旅
            </Title>

            <Paragraph style={{ color: '#dbeafe', fontSize: '18px', lineHeight: '1.6', marginBottom: '2rem' }}>
              注册ProxyHub账户，立即享受专业的代理服务。我们为新用户提供免费试用额度，
              让您体验企业级的网络代理解决方案。
            </Paragraph>

            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Gift className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>新用户免费试用额度</Text>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Zap className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>即时激活，快速上手</Text>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Shield className="text-white" size={16} />
                </div>
                <Text style={{ color: '#dbeafe', fontSize: '16px' }}>企业级安全保障</Text>
              </div>
            </div>

            <div className="bg-white bg-opacity-10 rounded-xl p-6">
              <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>立即获得</Title>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="text-green-300" size={16} />
                  <Text style={{ color: '#dbeafe', fontSize: '14px' }}>¥10 免费试用额度</Text>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="text-green-300" size={16} />
                  <Text style={{ color: '#dbeafe', fontSize: '14px' }}>7天免费技术支持</Text>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="text-green-300" size={16} />
                  <Text style={{ color: '#dbeafe', fontSize: '14px' }}>完整功能体验</Text>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧注册表单 */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="max-w-md w-full space-y-8">
            {/* 移动端品牌信息 */}
            <div className="text-center lg:hidden">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Globe className="text-white" size={24} />
                </div>
                <Title level={2} style={{ margin: 0, color: '#1f2937' }}>ProxyHub</Title>
              </div>
              <Text style={{ color: '#6b7280', fontSize: '16px' }}>专业的代理服务平台</Text>
            </div>

            <div className="text-center">
              <Title level={2} style={{ marginBottom: '0.5rem', color: '#1f2937' }}>创建账户</Title>
              <Text style={{ color: '#6b7280', fontSize: '16px' }}>填写信息以创建您的ProxyHub账户</Text>
            </div>

            <Card className="shadow-2xl border-0" bodyStyle={{ padding: '2rem' }}>
              <Form
                name="register"
                onFinish={onFinish}
                autoComplete="off"
                layout="vertical"
                size="large"
              >
                <Form.Item
                  name="email"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>邮箱地址</span>}
                  rules={[
                    { required: true, message: '请输入邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' }
                  ]}
                >
                  <Input
                    prefix={<MailOutlined style={{ color: '#6b7280', fontSize: '16px' }} />}
                    placeholder="请输入您的邮箱地址"
                    className="h-12 rounded-lg"
                    style={{ fontSize: '16px' }}
                  />
                </Form.Item>

                <Form.Item
                  name="username"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>用户名</span>}
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 2, message: '用户名至少2个字符' },
                    { max: 20, message: '用户名最多20个字符' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined style={{ color: '#6b7280', fontSize: '16px' }} />}
                    placeholder="请输入用户名"
                    className="h-12 rounded-lg"
                    style={{ fontSize: '16px' }}
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>密码</span>}
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 8, message: '密码至少8个字符' },
                    {
                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                      message: '密码必须包含大小写字母和数字'
                    }
                  ]}
                >
                  <div>
                    <Input.Password
                      prefix={<LockOutlined style={{ color: '#6b7280', fontSize: '16px' }} />}
                      placeholder="请输入密码"
                      className="h-12 rounded-lg"
                      style={{ fontSize: '16px' }}
                      iconRender={(visible) => (visible ? <EyeOutlined style={{ color: '#6b7280' }} /> : <EyeInvisibleOutlined style={{ color: '#6b7280' }} />)}
                      onChange={(e) => checkPasswordStrength(e.target.value)}
                    />
                    {passwordStrength > 0 && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between mb-1">
                          <Text style={{ fontSize: '12px', color: '#6b7280' }}>密码强度</Text>
                          <Text style={{ fontSize: '12px', color: getPasswordStrengthColor() }}>
                            {getPasswordStrengthText()}
                          </Text>
                        </div>
                        <Progress
                          percent={passwordStrength}
                          strokeColor={getPasswordStrengthColor()}
                          showInfo={false}
                          size="small"
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  label={<span style={{ color: '#374151', fontWeight: 500, fontSize: '14px' }}>确认密码</span>}
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined style={{ color: '#6b7280', fontSize: '16px' }} />}
                    placeholder="请再次输入密码"
                    className="h-12 rounded-lg"
                    style={{ fontSize: '16px' }}
                    iconRender={(visible) => (visible ? <EyeOutlined style={{ color: '#6b7280' }} /> : <EyeInvisibleOutlined style={{ color: '#6b7280' }} />)}
                  />
                </Form.Item>

                <Form.Item
                  name="agreement"
                  valuePropName="checked"
                  rules={[
                    {
                      validator: (_, value) =>
                        value ? Promise.resolve() : Promise.reject(new Error('请同意服务条款'))
                    }
                  ]}
                  className="!mb-6"
                >
                  <Checkbox style={{ color: '#374151' }}>
                    <span style={{ color: '#374151', fontSize: '14px' }}>
                      我已阅读并同意{' '}
                      <Link href="/terms" style={{ color: '#3b82f6' }} className="hover:text-blue-500">
                        服务条款
                      </Link>
                      {' '}和{' '}
                      <Link href="/privacy" style={{ color: '#3b82f6' }} className="hover:text-blue-500">
                        隐私政策
                      </Link>
                    </span>
                  </Checkbox>
                </Form.Item>

                <Form.Item className="!mb-4">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className="h-12 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    style={{
                      fontSize: '16px',
                      fontWeight: 500,
                      background: 'linear-gradient(to right, #3b82f6, #4f46e5)',
                      border: 'none'
                    }}
                  >
                    {loading ? '注册中...' : '创建账户'}
                  </Button>
                </Form.Item>
              </Form>

              <Divider style={{ margin: '1.5rem 0' }}>
                <Text style={{ color: '#9ca3af', fontSize: '14px' }}>或者</Text>
              </Divider>

              <div className="text-center">
                <Text style={{ color: '#6b7280', fontSize: '14px' }}>
                  已有账户？{' '}
                  <Link href="/auth/login" style={{ color: '#3b82f6', fontWeight: 500 }} className="hover:text-blue-500">
                    立即登录
                  </Link>
                </Text>
              </div>
            </Card>

            {/* 底部链接 */}
            <div className="text-center space-x-6">
              <Link href="/privacy" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">隐私政策</Link>
              <Link href="/terms" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">服务条款</Link>
              <Link href="/support" style={{ color: '#9ca3af', fontSize: '14px' }} className="hover:text-gray-700">技术支持</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
