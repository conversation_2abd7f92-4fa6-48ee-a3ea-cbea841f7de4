@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 确保 Ant Design 组件样式正确显示 */
.ant-layout-header {
  background: white !important;
}

.ant-layout-footer {
  background: #111827 !important;
}

.ant-typography {
  color: inherit !important;
}

.ant-typography.ant-typography-title {
  color: inherit !important;
  margin: 0 !important;
}

.ant-typography.ant-typography-paragraph {
  color: inherit !important;
}

/* 确保链接颜色正确 */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: inherit;
}

/* 修复可能的透明度问题 */
.ant-layout-header .ant-typography,
.ant-layout-footer .ant-typography {
  opacity: 1 !important;
}

/* 加载页面动画 */
@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

/* 渐变文字效果 */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

/* Dashboard侧边栏菜单样式 */
.ant-menu-inline .ant-menu-item {
  margin: 4px 0 !important;
  border-radius: 8px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.ant-menu-inline .ant-menu-item:hover {
  background-color: #f3f4f6 !important;
}

.ant-menu-inline .ant-menu-item-selected {
  background-color: #eff6ff !important;
  color: #3b82f6 !important;
}

.ant-menu-inline .ant-menu-item-selected .ant-menu-item-icon {
  color: #3b82f6 !important;
}

.ant-menu-inline .ant-menu-item .ant-menu-item-icon {
  font-size: 16px !important;
  color: #6b7280 !important;
}

.ant-menu-inline .ant-menu-item span {
  color: #374151 !important;
  font-size: 14px !important;
}
