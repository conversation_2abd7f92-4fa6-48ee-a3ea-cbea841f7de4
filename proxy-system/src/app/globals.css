@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 确保 Ant Design 组件样式正确显示 */
.ant-layout-header {
  background: white !important;
}

.ant-layout-footer {
  background: #111827 !important;
}

.ant-typography {
  color: inherit !important;
}

.ant-typography.ant-typography-title {
  color: inherit !important;
  margin: 0 !important;
}

.ant-typography.ant-typography-paragraph {
  color: inherit !important;
}

/* 确保链接颜色正确 */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: inherit;
}

/* 修复可能的透明度问题 */
.ant-layout-header .ant-typography,
.ant-layout-footer .ant-typography {
  opacity: 1 !important;
}
