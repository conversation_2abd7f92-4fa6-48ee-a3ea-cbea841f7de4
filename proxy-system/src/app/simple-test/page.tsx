'use client';

import React, { useEffect } from 'react';
import { message } from 'antd';

export default function SimpleTestPage() {
  useEffect(() => {
    console.log('🔍 页面加载完成，message对象:', message);

    // 配置message
    message.config({
      top: 100,
      duration: 3,
      maxCount: 3,
    });

    console.log('✅ message配置完成');

    // 延迟2秒后自动显示一个测试消息
    setTimeout(() => {
      console.log('🔍 自动显示测试消息');
      try {
        message.info('🎉 页面加载完成！这是一个自动显示的测试消息。');
        console.log('✅ 自动消息显示成功');
      } catch (error) {
        console.error('❌ 自动消息显示失败:', error);
      }
    }, 2000);
  }, []);

  const testMessage = () => {
    console.log('🔍 开始测试消息');
    
    try {
      // 最简单的消息调用
      message.success('这是一个测试消息');
      console.log('✅ 消息调用成功');
    } catch (error) {
      console.error('❌ 消息调用失败:', error);
      alert('消息调用失败: ' + error);
    }
  };

  const testWithStyle = () => {
    console.log('🔍 开始测试带样式的消息');
    
    try {
      message.success({
        content: '✅ 这是一个带样式的成功消息！',
        duration: 3,
        style: {
          marginTop: '100px',
          fontSize: '16px'
        }
      });
      console.log('✅ 带样式的消息调用成功');
    } catch (error) {
      console.error('❌ 带样式的消息调用失败:', error);
      alert('带样式的消息调用失败: ' + error);
    }
  };

  const testAlert = () => {
    alert('✅ JavaScript和React正常工作！');
  };

  return (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <h1>简单消息测试页面</h1>
      <p>这个页面用于测试最基础的消息功能</p>
      
      <div style={{ marginTop: '30px' }}>
        <button 
          onClick={testAlert}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          测试JavaScript
        </button>
        
        <button 
          onClick={testMessage}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          测试简单消息
        </button>
        
        <button 
          onClick={testWithStyle}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#722ed1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          测试带样式消息
        </button>
      </div>
      
      <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
        <p>请打开浏览器开发者工具查看控制台日志</p>
        <p>如果消息没有显示，请检查控制台是否有错误信息</p>
      </div>
    </div>
  );
}
