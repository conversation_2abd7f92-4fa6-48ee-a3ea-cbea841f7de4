// 数据库初始化工具（用于演示）
// 在实际生产环境中，应该使用专门的数据库迁移工具

import mysql from 'mysql2/promise';

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'proxy_system',
  port: parseInt(process.env.DB_PORT || '3306'),
};

// 创建系统设置表
const createSystemSettingsTable = `
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);
`;

// 插入默认设置
const insertDefaultSettings = `
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('px6_api_key', '', 'PX6 API密钥'),
('px6_api_url', 'https://api.px6.io', 'PX6 API地址'),
('max_proxies_per_user', '10', '每用户最大代理数量'),
('default_proxy_duration', '30', '默认代理时长（天）'),
('enable_auto_renewal', 'true', '启用自动续费'),
('maintenance_mode', 'false', '维护模式'),
('system_notice', '', '系统公告')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
description = VALUES(description);
`;

// 初始化数据库
export async function initializeDatabase() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // 创建系统设置表
    await connection.execute(createSystemSettingsTable);
    console.log('✓ 系统设置表创建成功');
    
    // 插入默认设置
    await connection.execute(insertDefaultSettings);
    console.log('✓ 默认设置插入成功');
    
    return { success: true, message: '数据库初始化成功' };
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return { success: false, error: error };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 检查数据库连接
export async function checkDatabaseConnection() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    await connection.ping();
    return { success: true, message: '数据库连接正常' };
  } catch (error) {
    console.error('数据库连接失败:', error);
    return { success: false, error: error };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
