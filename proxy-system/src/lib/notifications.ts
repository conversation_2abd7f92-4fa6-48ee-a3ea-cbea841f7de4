// 全局通知配置和工具函数
import { message, notification } from 'antd';

// 配置全局消息样式
message.config({
  top: 100,
  duration: 3,
  maxCount: 3,
  rtl: false,
});

// 配置全局通知样式
notification.config({
  placement: 'topRight',
  top: 100,
  duration: 4.5,
  rtl: false,
});

// 通用消息样式
const messageStyle = {
  marginTop: '20vh',
  fontSize: '14px',
};

// 成功消息
export const showSuccess = (content: string, duration = 3) => {
  return message.success({
    content: `✅ ${content}`,
    duration,
    style: messageStyle,
  });
};

// 错误消息
export const showError = (content: string, duration = 4) => {
  return message.error({
    content: `❌ ${content}`,
    duration,
    style: messageStyle,
  });
};

// 警告消息
export const showWarning = (content: string, duration = 3) => {
  return message.warning({
    content: `⚠️ ${content}`,
    duration,
    style: messageStyle,
  });
};

// 信息消息
export const showInfo = (content: string, duration = 3) => {
  return message.info({
    content: `ℹ️ ${content}`,
    duration,
    style: messageStyle,
  });
};

// 加载消息
export const showLoading = (content: string) => {
  return message.loading({
    content,
    duration: 0, // 不自动关闭
    style: messageStyle,
  });
};

// 认证相关的特定消息
export const authMessages = {
  loginSuccess: () => showSuccess('登录成功！正在跳转到控制台...', 2),
  loginError: (error?: string) => showError(error || '登录失败，请检查您的邮箱和密码', 4),
  loginLoading: () => showLoading('正在登录中...'),
  
  registerSuccess: () => showSuccess('🎉 注册成功！欢迎加入ProxyHub！正在跳转到控制台...', 3),
  registerError: (error?: string) => showError(error || '注册失败，请检查您的信息后重试', 4),
  registerLoading: () => showLoading('正在创建您的账户...'),
  
  logoutSuccess: () => showSuccess('已成功退出登录，感谢您的使用！', 2),
  logoutError: () => showError('退出登录失败，请重试', 3),
  logoutLoading: () => showLoading('正在退出登录...'),
  
  agreementRequired: () => showWarning('请同意服务条款和隐私政策后再继续', 3),
};

// 设置相关的特定消息
export const settingsMessages = {
  saveSuccess: () => showSuccess('设置保存成功！配置已生效', 3),
  saveError: (error?: string) => showError(error || '保存失败，请检查网络连接后重试', 4),
  saveLoading: () => showLoading('正在保存设置...'),
  
  loadError: () => showError('加载设置失败', 3),
  
  apiTestSuccess: (info?: string) => showSuccess(`API连接测试成功！${info || ''}`, 4),
  apiTestError: (error?: string) => showError(error || 'API连接测试失败，请检查网络连接', 4),
  apiTestLoading: () => showLoading('正在测试API连接...'),
  apiKeyRequired: () => showWarning('请先输入PX6 API密钥', 3),
  
  dbInitSuccess: () => showSuccess('数据库初始化成功', 3),
  dbInitError: (error?: string) => showError(error || '数据库初始化失败', 4),
};

// 通用操作消息
export const operationMessages = {
  success: (operation: string) => showSuccess(`${operation}成功`, 3),
  error: (operation: string, error?: string) => showError(`${operation}失败${error ? `: ${error}` : ''}`, 4),
  loading: (operation: string) => showLoading(`正在${operation}...`),
};

// 高级通知（带图标和详细信息）
export const showNotification = {
  success: (title: string, description?: string, duration = 4.5) => {
    notification.success({
      message: title,
      description,
      duration,
      style: {
        borderLeft: '4px solid #52c41a',
      },
    });
  },
  
  error: (title: string, description?: string, duration = 6) => {
    notification.error({
      message: title,
      description,
      duration,
      style: {
        borderLeft: '4px solid #ff4d4f',
      },
    });
  },
  
  warning: (title: string, description?: string, duration = 4.5) => {
    notification.warning({
      message: title,
      description,
      duration,
      style: {
        borderLeft: '4px solid #faad14',
      },
    });
  },
  
  info: (title: string, description?: string, duration = 4.5) => {
    notification.info({
      message: title,
      description,
      duration,
      style: {
        borderLeft: '4px solid #1890ff',
      },
    });
  },
};

// 确认对话框的通用配置
export const confirmConfig = {
  okText: '确认',
  cancelText: '取消',
  centered: true,
  maskClosable: true,
  width: 400,
};

// 导出message和notification以便直接使用
export { message, notification };
