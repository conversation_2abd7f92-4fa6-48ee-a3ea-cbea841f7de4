// 演示版本的认证系统 - 使用文件存储
import fs from 'fs';
import path from 'path';
import bcrypt from 'bcryptjs';
import { generateToken, verifyToken } from './utils';

const USERS_FILE = path.join(process.cwd(), 'data', 'users.json');

// 用户接口
interface DemoUser {
  id: number;
  email: string;
  username: string;
  password: string;
  role: 'admin' | 'user';
  balance: number;
  created_at: string;
}

// 确保数据目录存在
function ensureDataDir() {
  const dataDir = path.dirname(USERS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// 读取用户数据
function readUsers(): DemoUser[] {
  ensureDataDir();
  try {
    if (fs.existsSync(USERS_FILE)) {
      const data = fs.readFileSync(USERS_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('读取用户文件失败:', error);
  }
  return [];
}

// 保存用户数据
function saveUsers(users: DemoUser[]): boolean {
  ensureDataDir();
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('保存用户文件失败:', error);
    return false;
  }
}

// 初始化默认用户
export function initDefaultUsers() {
  const users = readUsers();
  
  // 检查是否已有管理员用户
  const adminExists = users.some(user => user.role === 'admin');
  
  if (!adminExists) {
    // 创建默认管理员
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
    const hashedPassword = bcrypt.hashSync(adminPassword, 10);
    
    const adminUser: DemoUser = {
      id: 1,
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: 'admin',
      balance: 0,
      created_at: new Date().toISOString()
    };
    
    users.push(adminUser);
    saveUsers(users);
    console.log('默认管理员用户已创建');
  }
}

// 用户登录
export async function loginUser(email: string, password: string) {
  const users = readUsers();
  const user = users.find(u => u.email === email);
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  const isValidPassword = bcrypt.compareSync(password, user.password);
  if (!isValidPassword) {
    throw new Error('密码错误');
  }
  
  // 生成token
  const token = generateToken({
    userId: user.id,
    email: user.email,
    role: user.role
  });
  
  // 返回用户信息（不包含密码）
  const { password: _, ...userWithoutPassword } = user;
  
  return {
    user: userWithoutPassword,
    token
  };
}

// 用户注册
export async function registerUser(email: string, username: string, password: string) {
  const users = readUsers();
  
  // 检查邮箱是否已存在
  if (users.some(u => u.email === email)) {
    throw new Error('邮箱已被注册');
  }
  
  // 检查用户名是否已存在
  if (users.some(u => u.username === username)) {
    throw new Error('用户名已被使用');
  }
  
  // 创建新用户
  const hashedPassword = bcrypt.hashSync(password, 10);
  const newUser: DemoUser = {
    id: Math.max(0, ...users.map(u => u.id)) + 1,
    email,
    username,
    password: hashedPassword,
    role: 'user',
    balance: 0,
    created_at: new Date().toISOString()
  };
  
  users.push(newUser);
  saveUsers(users);
  
  // 生成token
  const token = generateToken({
    userId: newUser.id,
    email: newUser.email,
    role: newUser.role
  });
  
  // 返回用户信息（不包含密码）
  const { password: _, ...userWithoutPassword } = newUser;
  
  return {
    user: userWithoutPassword,
    token
  };
}

// 根据ID获取用户
export function getUserById(id: number) {
  const users = readUsers();
  const user = users.find(u => u.id === id);
  
  if (!user) {
    return null;
  }
  
  // 返回用户信息（不包含密码）
  const { password: _, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

// 验证token并获取用户信息
export function verifyUserToken(token: string) {
  try {
    const payload = verifyToken(token);
    const user = getUserById(payload.userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    return user;
  } catch (error) {
    throw new Error('Token无效或已过期');
  }
}
