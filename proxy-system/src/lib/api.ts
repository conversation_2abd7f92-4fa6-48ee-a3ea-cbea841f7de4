// API工具函数

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
}

// 获取存储的token
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('token');
}

// 带认证的API请求
export async function apiRequest(url: string, options: ApiOptions = {}) {
  const token = getAuthToken();
  
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    method: options.method || 'GET',
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  if (options.body && options.method !== 'GET') {
    config.body = JSON.stringify(options.body);
  }

  const response = await fetch(url, config);

  if (response.status === 401) {
    // Token过期，清除本地存储并跳转到登录页
    localStorage.removeItem('token');
    window.location.href = '/auth/login';
    throw new Error('认证失败');
  }

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  return response.json();
}

// 系统设置相关API
export const settingsApi = {
  // 获取系统设置
  getSettings: () => apiRequest('/api/admin/settings'),
  
  // 保存系统设置
  saveSettings: (settings: any) => 
    apiRequest('/api/admin/settings', {
      method: 'POST',
      body: settings,
    }),
  
  // 测试PX6 API连接
  testPx6Connection: (apiKey: string, apiUrl: string) =>
    apiRequest('/api/admin/test-px6', {
      method: 'POST',
      body: { apiKey, apiUrl },
    }),
};

// 用户管理相关API
export const userApi = {
  // 获取用户列表
  getUsers: (page = 1, limit = 10) => 
    apiRequest(`/api/admin/users?page=${page}&limit=${limit}`),
  
  // 获取用户详情
  getUser: (id: number) => 
    apiRequest(`/api/admin/users/${id}`),
  
  // 更新用户
  updateUser: (id: number, data: any) =>
    apiRequest(`/api/admin/users/${id}`, {
      method: 'PUT',
      body: data,
    }),
  
  // 删除用户
  deleteUser: (id: number) =>
    apiRequest(`/api/admin/users/${id}`, {
      method: 'DELETE',
    }),
};

// 代理管理相关API
export const proxyApi = {
  // 获取代理列表
  getProxies: (page = 1, limit = 10) =>
    apiRequest(`/api/admin/proxies?page=${page}&limit=${limit}`),
  
  // 创建代理
  createProxy: (data: any) =>
    apiRequest('/api/admin/proxies', {
      method: 'POST',
      body: data,
    }),
  
  // 更新代理
  updateProxy: (id: number, data: any) =>
    apiRequest(`/api/admin/proxies/${id}`, {
      method: 'PUT',
      body: data,
    }),
  
  // 删除代理
  deleteProxy: (id: number) =>
    apiRequest(`/api/admin/proxies/${id}`, {
      method: 'DELETE',
    }),
};

// 统计数据相关API
export const statsApi = {
  // 获取系统统计
  getSystemStats: () =>
    apiRequest('/api/admin/stats/system'),
  
  // 获取用户统计
  getUserStats: (userId?: number) =>
    apiRequest(`/api/admin/stats/users${userId ? `/${userId}` : ''}`),
  
  // 获取API使用统计
  getApiStats: (days = 7) =>
    apiRequest(`/api/admin/stats/api?days=${days}`),
};
