# ProxyHub - 专业代理服务平台

<div align="center">

![ProxyHub Logo](https://img.shields.io/badge/ProxyHub-专业代理服务-blue?style=for-the-badge&logo=globe)

[![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Ant Design](https://img.shields.io/badge/Ant%20Design-5.0-1890ff?style=flat-square&logo=antdesign)](https://ant.design/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)

**企业级代理服务管理平台，提供稳定、安全、高速的网络代理解决方案**

[🚀 在线演示](http://localhost:3000) | [📖 文档](#文档) | [🐛 问题反馈](#问题反馈)

</div>

## ✨ 项目特色

### 🎨 现代化UI设计
- **企业级界面**：专业的视觉设计，符合现代Web标准
- **响应式布局**：完美适配桌面端和移动端
- **动态加载效果**：多步骤加载动画，提升用户体验
- **一致性设计**：统一的品牌标识和设计语言

### 🔐 完整的用户系统
- **用户注册/登录**：安全的身份验证系统
- **密码强度检测**：实时密码安全性评估
- **会话管理**：JWT令牌认证，安全可靠
- **用户状态管理**：全局状态管理，无缝用户体验

### 📱 丰富的页面功能
- **首页展示**：产品介绍和价值主张
- **产品服务**：详细的代理服务类型说明
- **价格方案**：灵活的定价策略和对比
- **技术支持**：完整的客服和FAQ系统

### 🛠️ 技术架构优势
- **Next.js 15**：最新的React全栈框架
- **TypeScript**：类型安全的开发体验
- **组件化设计**：可复用的UI组件库
- **性能优化**：代码分割和懒加载

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 9.0 或更高版本

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd proxy-system
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
# 复制环境变量文件
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
proxy-system/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── auth/              # 认证相关页面
│   │   │   ├── login/         # 登录页面
│   │   │   └── register/      # 注册页面
│   │   ├── products/          # 产品服务页面
│   │   ├── pricing/           # 价格方案页面
│   │   ├── support/           # 技术支持页面
│   │   ├── dashboard/         # 用户控制台
│   │   ├── api/               # API路由
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # 可复用组件
│   │   ├── AppHeader.tsx      # 顶部导航栏
│   │   ├── AppFooter.tsx      # 底部导航栏
│   │   ├── AuthProvider.tsx   # 认证状态管理
│   │   └── LoadingScreen.tsx  # 加载页面
│   └── lib/                   # 工具库
├── public/                    # 静态资源
├── .env.local                 # 环境变量
├── next.config.js            # Next.js配置
├── tailwind.config.js        # Tailwind配置
├── tsconfig.json             # TypeScript配置
└── package.json              # 项目依赖
```

## 🎯 核心功能

### 🏠 首页 (/)
- **品牌展示**：ProxyHub品牌介绍和价值主张
- **产品概览**：三大代理服务类型展示
- **客户评价**：用户反馈和成功案例
- **CTA引导**：引导用户注册和试用

### 📦 产品服务 (/products)
- **IPv4独享代理**：高性能独享IP服务
- **IPv6代理**：下一代协议支持
- **共享代理**：经济实惠的共享方案
- **价格对比表**：详细的功能和价格对比

### 💰 价格方案 (/pricing)
- **三档套餐**：基础版、专业版、企业版
- **月付/年付切换**：灵活的计费周期
- **功能对比**：清晰的套餐差异展示
- **常见问题**：解答用户疑虑

### 🛠️ 技术支持 (/support)
- **多种联系方式**：在线客服、邮件、电话
- **工单系统**：完整的问题提交流程
- **FAQ系统**：常见问题快速解答
- **服务时间**：明确的支持时间说明

### 🔐 用户认证
- **登录系统**：安全的用户身份验证
- **注册流程**：友好的新用户注册体验
- **密码安全**：实时密码强度检测
- **会话管理**：自动登录状态维护

## 🛠️ 技术栈

### 前端框架
- **Next.js 15.3.4** - React全栈框架
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的JavaScript

### UI组件库
- **Ant Design 5.0** - 企业级UI组件库
- **Tailwind CSS 3.0** - 原子化CSS框架
- **Lucide React** - 现代化图标库

### 状态管理
- **React Context** - 全局状态管理
- **React Hooks** - 组件状态管理

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Turbopack** - 快速构建工具

## 🎨 设计特色

### 视觉设计
- **现代化界面**：简洁、专业的设计风格
- **渐变背景**：多层次的视觉效果
- **动画效果**：平滑的过渡和交互动画
- **响应式布局**：适配各种屏幕尺寸

### 用户体验
- **加载动画**：多步骤的加载进度展示
- **表单验证**：实时的输入验证反馈
- **错误处理**：友好的错误提示信息
- **无障碍访问**：符合WCAG标准

### 品牌一致性
- **统一配色**：蓝色系主色调
- **图标系统**：一致的图标使用规范
- **字体层次**：清晰的信息层级
- **间距规范**：统一的布局间距

## 📱 页面展示

### 🏠 首页
- 品牌展示区域
- 产品特性介绍
- 客户评价展示
- 底部导航栏

### 🔐 登录页面
- 分屏式布局设计
- 左侧品牌信息展示
- 右侧登录表单
- 记住我和忘记密码功能

### 📝 注册页面
- 新用户福利展示
- 密码强度实时检测
- 服务条款同意
- 免费试用额度说明

### 📦 产品页面
- 三种代理类型详细介绍
- 功能特性对比表
- 价格信息展示
- 立即购买引导

### 💰 价格页面
- 三档套餐对比
- 月付年付切换
- 节省金额计算
- 常见问题解答

### 🛠️ 支持页面
- 多种联系方式
- 工单提交系统
- FAQ折叠展示
- 服务时间说明

## 🚀 部署指南

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 代码检查
npm run lint

# 类型检查
npm run type-check
```

### 生产环境
```bash
# 构建项目
npm run build

# 启动生产服务器
npm start

# 预览构建结果
npm run preview
```

### Docker部署
```dockerfile
# Dockerfile示例
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### 环境变量配置
```bash
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
DATABASE_URL=your-database-url

# PX6 API配置已移至后台管理页面
# 请在 /dashboard/settings 页面配置 PX6_API_KEY
```

### PX6 API密钥配置

**重要**: PX6_API_KEY 密钥配置已从 `.env.local` 文件迁移到后台管理页面，提供更安全的配置管理。

#### 配置步骤：
1. 使用管理员账户登录系统
2. 访问 `/dashboard/settings` 页面
3. 在 "PX6 API配置" 部分输入密钥
4. 测试API连接并保存设置

详细配置指南请参考：[PX6 API配置文档](docs/PX6_API_CONFIGURATION.md)

## 🔧 开发指南

### 代码规范
- **TypeScript**：严格的类型检查
- **ESLint**：代码质量保证
- **Prettier**：统一的代码格式
- **组件命名**：PascalCase命名规范

### 组件开发
```tsx
// 组件示例
interface ComponentProps {
  title: string;
  children: React.ReactNode;
}

export default function Component({ title, children }: ComponentProps) {
  return (
    <div className="component-wrapper">
      <h1>{title}</h1>
      {children}
    </div>
  );
}
```

### 样式规范
```css
/* 使用Tailwind CSS类名 */
.button-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg;
}

/* 自定义CSS变量 */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #6b7280;
}
```

### API开发
```typescript
// API路由示例
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const data = await fetchData();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

## 📊 性能优化

### 构建优化
- **代码分割**：按路由自动分割
- **Tree Shaking**：移除未使用代码
- **图片优化**：Next.js Image组件
- **字体优化**：自动字体优化

### 运行时优化
- **懒加载**：组件按需加载
- **缓存策略**：合理的缓存配置
- **预加载**：关键资源预加载
- **压缩**：Gzip/Brotli压缩

### 监控指标
- **Core Web Vitals**：用户体验指标
- **Bundle Size**：包大小监控
- **Load Time**：页面加载时间
- **Error Rate**：错误率监控

## 🧪 测试

### 单元测试
```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage

# 监听模式
npm run test:watch
```

### E2E测试
```bash
# Playwright测试
npm run test:e2e

# 可视化测试
npm run test:e2e:ui
```

### 测试示例
```typescript
import { render, screen } from '@testing-library/react';
import Component from './Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

## 🔒 安全性

### 认证安全
- **JWT令牌**：安全的身份验证
- **密码加密**：bcrypt哈希加密
- **会话管理**：安全的会话处理
- **CSRF保护**：跨站请求伪造防护

### 数据安全
- **输入验证**：严格的数据验证
- **SQL注入防护**：参数化查询
- **XSS防护**：内容安全策略
- **HTTPS强制**：安全传输协议

### 隐私保护
- **数据最小化**：只收集必要数据
- **匿名化处理**：敏感数据匿名化
- **访问控制**：基于角色的权限控制
- **审计日志**：操作记录追踪

## 📈 监控与分析

### 性能监控
- **Real User Monitoring**：真实用户监控
- **Synthetic Monitoring**：合成监控
- **Error Tracking**：错误追踪
- **Performance Metrics**：性能指标

### 业务分析
- **用户行为分析**：页面访问统计
- **转化率分析**：注册转化追踪
- **A/B测试**：功能效果测试
- **用户反馈**：满意度调查

## 🤝 贡献指南

### 开发流程
1. **Fork项目**：创建项目副本
2. **创建分支**：`git checkout -b feature/new-feature`
3. **提交代码**：`git commit -m "Add new feature"`
4. **推送分支**：`git push origin feature/new-feature`
5. **创建PR**：提交Pull Request

### 代码审查
- **代码质量**：符合项目规范
- **测试覆盖**：包含相应测试
- **文档更新**：更新相关文档
- **性能影响**：评估性能影响

### 问题反馈
- **Bug报告**：详细的问题描述
- **功能请求**：清晰的需求说明
- **改进建议**：具体的优化方案

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 👥 团队

- **项目负责人**：[Your Name]
- **前端开发**：[Frontend Team]
- **后端开发**：[Backend Team]
- **UI/UX设计**：[Design Team]

## 📞 联系我们

- **官方网站**：[https://proxyhub.com](https://proxyhub.com)
- **技术支持**：<EMAIL>
- **商务合作**：<EMAIL>
- **GitHub Issues**：[项目Issues页面](https://github.com/your-org/proxy-system/issues)

## 🙏 致谢

感谢以下开源项目的支持：

- [Next.js](https://nextjs.org/) - React全栈框架
- [Ant Design](https://ant.design/) - 企业级UI组件库
- [Tailwind CSS](https://tailwindcss.com/) - 原子化CSS框架
- [Lucide](https://lucide.dev/) - 现代化图标库
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的JavaScript

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**

Made with ❤️ by ProxyHub Team

</div>
