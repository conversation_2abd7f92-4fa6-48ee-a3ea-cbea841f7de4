/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 10.01h.01", key: "1e9xi7" }],
  ["path", { d: "M10 14.01h.01", key: "ac23bv" }],
  ["path", { d: "M14 10.01h.01", key: "2wfrvf" }],
  ["path", { d: "M14 14.01h.01", key: "8tw8yn" }],
  ["path", { d: "M18 6v11.5", key: "dkbidh" }],
  ["path", { d: "M6 6v12", key: "vkc79e" }],
  ["rect", { x: "2", y: "6", width: "20", height: "12", rx: "2", key: "1wpnh2" }]
];
const Bandage = createLucideIcon("bandage", __iconNode);

export { __iconNode, Bandage as default };
//# sourceMappingURL=bandage.js.map
