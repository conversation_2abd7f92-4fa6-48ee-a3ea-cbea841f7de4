{"name": "proxy-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "antd": "^5.26.3", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "dotenv": "^17.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mysql2": "^3.14.1", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}