#!/usr/bin/env node

/**
 * 开发环境优化脚本
 * 用于提高开发模式的启动速度和编译性能
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始优化开发环境...');

// 1. 创建必要的目录
const directories = [
  'data',
  '.next/cache',
  'node_modules/.cache'
];

directories.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dir}`);
  }
});

// 2. 初始化用户数据文件
const usersFile = path.join(process.cwd(), 'data', 'users.json');
if (!fs.existsSync(usersFile)) {
  const defaultUsers = [];
  fs.writeFileSync(usersFile, JSON.stringify(defaultUsers, null, 2));
  console.log('✅ 初始化用户数据文件');
}

// 3. 初始化系统设置文件
const settingsFile = path.join(process.cwd(), 'data', 'system_settings.json');
if (!fs.existsSync(settingsFile)) {
  const defaultSettings = {
    px6_api_key: '',
    px6_api_url: 'https://api.px6.io',
    max_proxies_per_user: 10,
    default_proxy_duration: 30,
    enable_auto_renewal: true,
    maintenance_mode: false,
    system_notice: ''
  };
  fs.writeFileSync(settingsFile, JSON.stringify(defaultSettings, null, 2));
  console.log('✅ 初始化系统设置文件');
}

// 4. 检查环境变量
const envFile = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');
  
  // 检查必要的环境变量
  const requiredVars = ['JWT_SECRET', 'ADMIN_EMAIL', 'ADMIN_PASSWORD'];
  const missingVars = requiredVars.filter(varName => !envContent.includes(varName));
  
  if (missingVars.length > 0) {
    console.log(`⚠️  缺少环境变量: ${missingVars.join(', ')}`);
  } else {
    console.log('✅ 环境变量检查通过');
  }
} else {
  console.log('⚠️  .env.local 文件不存在');
}

// 5. 清理缓存（可选）
if (process.argv.includes('--clear-cache')) {
  const cacheDir = path.join(process.cwd(), '.next');
  if (fs.existsSync(cacheDir)) {
    fs.rmSync(cacheDir, { recursive: true, force: true });
    console.log('✅ 清理 Next.js 缓存');
  }
}

// 6. 显示开发提示
console.log('\n📋 开发环境优化完成！');
console.log('\n💡 开发提示:');
console.log('   • 使用 npm run dev 启动开发服务器');
console.log('   • 首次访问可能需要编译，请耐心等待');
console.log('   • 默认管理员账户: <EMAIL> / admin123456');
console.log('   • 设置页面: http://localhost:3000/dashboard/settings');
console.log('   • 如遇到问题，请检查控制台日志');

console.log('\n🔧 性能优化建议:');
console.log('   • 关闭不必要的浏览器标签页');
console.log('   • 确保有足够的内存空间');
console.log('   • 使用 --turbo 标志启动以获得更快的编译速度');

console.log('\n🚀 准备就绪！可以开始开发了。');
