{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, ApiResponse } from '@/types';\nimport axios from 'axios';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (email: string, username: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // 获取用户信息\n  const fetchUser = async () => {\n    try {\n      const response = await axios.get<ApiResponse<User>>('/api/auth/me');\n      if (response.data.success && response.data.data) {\n        setUser(response.data.data);\n      } else {\n        setUser(null);\n      }\n    } catch (error) {\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登录\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await axios.post<ApiResponse<{ user: User; token: string }>>('/api/auth/login', {\n        email,\n        password\n      });\n\n      if (response.data.success && response.data.data) {\n        setUser(response.data.data.user);\n      } else {\n        throw new Error(response.data.error || '登录失败');\n      }\n    } catch (error) {\n      if (axios.isAxiosError(error) && error.response?.data?.error) {\n        throw new Error(error.response.data.error);\n      }\n      throw error;\n    }\n  };\n\n  // 注册\n  const register = async (email: string, username: string, password: string) => {\n    try {\n      const response = await axios.post<ApiResponse<{ user: User; token: string }>>('/api/auth/register', {\n        email,\n        username,\n        password\n      });\n\n      if (response.data.success && response.data.data) {\n        setUser(response.data.data.user);\n      } else {\n        throw new Error(response.data.error || '注册失败');\n      }\n    } catch (error) {\n      if (axios.isAxiosError(error) && error.response?.data?.error) {\n        throw new Error(error.response.data.error);\n      }\n      throw error;\n    }\n  };\n\n  // 退出登录\n  const logout = async () => {\n    try {\n      await axios.post('/api/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n    }\n  };\n\n  // 刷新用户信息\n  const refreshUser = async () => {\n    await fetchUser();\n  };\n\n  // 检查是否为管理员\n  const isAdmin = user?.role === 'admin';\n\n  // 初始化时获取用户信息\n  useEffect(() => {\n    fetchUser();\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    refreshUser,\n    isAdmin\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,SAAS;IACT,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAoB;YACpD,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC5B,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,KAAK;IACL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAA6C,mBAAmB;gBAC/F;gBACA;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;YACjC,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI;YACzC;QACF,EAAE,OAAO,OAAO;YACd,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,MAAM,OAAO;gBAC5D,MAAM,IAAI,MAAM,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;YAC3C;YACA,MAAM;QACR;IACF;IAEA,KAAK;IACL,MAAM,WAAW,OAAO,OAAe,UAAkB;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAA6C,sBAAsB;gBAClG;gBACA;gBACA;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;YACjC,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI;YACzC;QACF,EAAE,OAAO,OAAO;YACd,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,MAAM,OAAO;gBAC5D,MAAM,IAAI,MAAM,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;YAC3C;YACA,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,SAAS;QACb,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,MAAM;IACR;IAEA,WAAW;IACX,MAAM,UAAU,MAAM,SAAS;IAE/B,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}]}