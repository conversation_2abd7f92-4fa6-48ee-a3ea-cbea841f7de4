{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthProvider.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthProvider.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iEACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,iEACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthProvider.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthProvider.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6CACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { AntdRegistry } from '@ant-design/nextjs-registry';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { AuthProvider } from '@/components/AuthProvider';\nimport \"./globals.css\";\n\nexport const metadata: Metadata = {\n  title: \"代理系统\",\n  description: \"PX6代理购买和管理系统\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <body>\n        <AntdRegistry>\n          <ConfigProvider locale={zhCN}>\n            <AuthProvider>\n              {children}\n            </AuthProvider>\n          </ConfigProvider>\n        </AntdRegistry>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBACC,cAAA,8OAAC,sNAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,QAAQ,uIAAA,CAAA,UAAI;8BAC1B,cAAA,8OAAC,kIAAA,CAAA,eAAY;kCACV;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40ant-design/nextjs-registry/es/AntdRegistry.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40ant-design/nextjs-registry/es/AntdRegistry.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40ant-design/nextjs-registry/es/index.js"], "sourcesContent": ["export { default as AntdRegistry } from \"./AntdRegistry\";"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/es/index.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Affix = registerClientReference(\n    function() { throw new Error(\"Attempted to call Affix() from the server but Affix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Affix\",\n);\nexport const Alert = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Alert\",\n);\nexport const Anchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON>chor() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Anchor\",\n);\nexport const App = registerClientReference(\n    function() { throw new Error(\"Attempted to call App() from the server but App is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"App\",\n);\nexport const AutoComplete = registerClientReference(\n    function() { throw new Error(\"Attempted to call AutoComplete() from the server but AutoComplete is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"AutoComplete\",\n);\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Avatar\",\n);\nexport const BackTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackTop() from the server but BackTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"BackTop\",\n);\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Badge\",\n);\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Button\",\n);\nexport const Calendar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Calendar() from the server but Calendar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Calendar\",\n);\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Card\",\n);\nexport const Carousel = registerClientReference(\n    function() { throw new Error(\"Attempted to call Carousel() from the server but Carousel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Carousel\",\n);\nexport const Cascader = registerClientReference(\n    function() { throw new Error(\"Attempted to call Cascader() from the server but Cascader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Cascader\",\n);\nexport const Checkbox = registerClientReference(\n    function() { throw new Error(\"Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Checkbox\",\n);\nexport const Col = registerClientReference(\n    function() { throw new Error(\"Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Col\",\n);\nexport const Collapse = registerClientReference(\n    function() { throw new Error(\"Attempted to call Collapse() from the server but Collapse is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Collapse\",\n);\nexport const ColorPicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorPicker() from the server but ColorPicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"ColorPicker\",\n);\nexport const ConfigProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"ConfigProvider\",\n);\nexport const DatePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call DatePicker() from the server but DatePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"DatePicker\",\n);\nexport const Descriptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Descriptions() from the server but Descriptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Descriptions\",\n);\nexport const Divider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Divider() from the server but Divider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Divider\",\n);\nexport const Drawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Drawer() from the server but Drawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Drawer\",\n);\nexport const Dropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dropdown() from the server but Dropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Dropdown\",\n);\nexport const Empty = registerClientReference(\n    function() { throw new Error(\"Attempted to call Empty() from the server but Empty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Empty\",\n);\nexport const Flex = registerClientReference(\n    function() { throw new Error(\"Attempted to call Flex() from the server but Flex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Flex\",\n);\nexport const FloatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatButton() from the server but FloatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"FloatButton\",\n);\nexport const Form = registerClientReference(\n    function() { throw new Error(\"Attempted to call Form() from the server but Form is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Form\",\n);\nexport const Grid = registerClientReference(\n    function() { throw new Error(\"Attempted to call Grid() from the server but Grid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Grid\",\n);\nexport const Image = registerClientReference(\n    function() { throw new Error(\"Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Image\",\n);\nexport const Input = registerClientReference(\n    function() { throw new Error(\"Attempted to call Input() from the server but Input is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Input\",\n);\nexport const InputNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call InputNumber() from the server but InputNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"InputNumber\",\n);\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Layout\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"List\",\n);\nexport const Mentions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mentions() from the server but Mentions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Mentions\",\n);\nexport const Menu = registerClientReference(\n    function() { throw new Error(\"Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Menu\",\n);\nexport const Modal = registerClientReference(\n    function() { throw new Error(\"Attempted to call Modal() from the server but Modal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Modal\",\n);\nexport const Pagination = registerClientReference(\n    function() { throw new Error(\"Attempted to call Pagination() from the server but Pagination is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Pagination\",\n);\nexport const Popconfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popconfirm() from the server but Popconfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Popconfirm\",\n);\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Popover\",\n);\nexport const Progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call Progress() from the server but Progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Progress\",\n);\nexport const QRCode = registerClientReference(\n    function() { throw new Error(\"Attempted to call QRCode() from the server but QRCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"QRCode\",\n);\nexport const Radio = registerClientReference(\n    function() { throw new Error(\"Attempted to call Radio() from the server but Radio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Radio\",\n);\nexport const Rate = registerClientReference(\n    function() { throw new Error(\"Attempted to call Rate() from the server but Rate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Rate\",\n);\nexport const Result = registerClientReference(\n    function() { throw new Error(\"Attempted to call Result() from the server but Result is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Result\",\n);\nexport const Row = registerClientReference(\n    function() { throw new Error(\"Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Row\",\n);\nexport const Segmented = registerClientReference(\n    function() { throw new Error(\"Attempted to call Segmented() from the server but Segmented is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Segmented\",\n);\nexport const Select = registerClientReference(\n    function() { throw new Error(\"Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Select\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Skeleton\",\n);\nexport const Slider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Slider() from the server but Slider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Slider\",\n);\nexport const Space = registerClientReference(\n    function() { throw new Error(\"Attempted to call Space() from the server but Space is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Space\",\n);\nexport const Spin = registerClientReference(\n    function() { throw new Error(\"Attempted to call Spin() from the server but Spin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Spin\",\n);\nexport const Splitter = registerClientReference(\n    function() { throw new Error(\"Attempted to call Splitter() from the server but Splitter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Splitter\",\n);\nexport const Statistic = registerClientReference(\n    function() { throw new Error(\"Attempted to call Statistic() from the server but Statistic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Statistic\",\n);\nexport const Steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call Steps() from the server but Steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Steps\",\n);\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Switch\",\n);\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Table\",\n);\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tabs\",\n);\nexport const Tag = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tag() from the server but Tag is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tag\",\n);\nexport const TimePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimePicker() from the server but TimePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"TimePicker\",\n);\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Timeline\",\n);\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tooltip\",\n);\nexport const Tour = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tour() from the server but Tour is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tour\",\n);\nexport const Transfer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Transfer() from the server but Transfer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Transfer\",\n);\nexport const Tree = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tree() from the server but Tree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tree\",\n);\nexport const TreeSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TreeSelect() from the server but TreeSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"TreeSelect\",\n);\nexport const Typography = registerClientReference(\n    function() { throw new Error(\"Attempted to call Typography() from the server but Typography is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Typography\",\n);\nexport const Upload = registerClientReference(\n    function() { throw new Error(\"Attempted to call Upload() from the server but Upload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Upload\",\n);\nexport const Watermark = registerClientReference(\n    function() { throw new Error(\"Attempted to call Watermark() from the server but Watermark is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Watermark\",\n);\nexport const message = registerClientReference(\n    function() { throw new Error(\"Attempted to call message() from the server but message is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"message\",\n);\nexport const notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call notification() from the server but notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"notification\",\n);\nexport const theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call theme() from the server but theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"theme\",\n);\nexport const unstableSetRender = registerClientReference(\n    function() { throw new Error(\"Attempted to call unstableSetRender() from the server but unstableSetRender is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"unstableSetRender\",\n);\nexport const version = registerClientReference(\n    function() { throw new Error(\"Attempted to call version() from the server but version is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"version\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/es/index.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Affix = registerClientReference(\n    function() { throw new Error(\"Attempted to call Affix() from the server but Affix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Affix\",\n);\nexport const Alert = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Alert\",\n);\nexport const Anchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call Anchor() from the server but <PERSON>chor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Anchor\",\n);\nexport const App = registerClientReference(\n    function() { throw new Error(\"Attempted to call App() from the server but App is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"App\",\n);\nexport const AutoComplete = registerClientReference(\n    function() { throw new Error(\"Attempted to call AutoComplete() from the server but AutoComplete is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"AutoComplete\",\n);\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Avatar\",\n);\nexport const BackTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackTop() from the server but BackTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"BackTop\",\n);\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Badge\",\n);\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Breadcrumb\",\n);\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Button\",\n);\nexport const Calendar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Calendar() from the server but Calendar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Calendar\",\n);\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Card\",\n);\nexport const Carousel = registerClientReference(\n    function() { throw new Error(\"Attempted to call Carousel() from the server but Carousel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Carousel\",\n);\nexport const Cascader = registerClientReference(\n    function() { throw new Error(\"Attempted to call Cascader() from the server but Cascader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Cascader\",\n);\nexport const Checkbox = registerClientReference(\n    function() { throw new Error(\"Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Checkbox\",\n);\nexport const Col = registerClientReference(\n    function() { throw new Error(\"Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Col\",\n);\nexport const Collapse = registerClientReference(\n    function() { throw new Error(\"Attempted to call Collapse() from the server but Collapse is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Collapse\",\n);\nexport const ColorPicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorPicker() from the server but ColorPicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"ColorPicker\",\n);\nexport const ConfigProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"ConfigProvider\",\n);\nexport const DatePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call DatePicker() from the server but DatePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"DatePicker\",\n);\nexport const Descriptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Descriptions() from the server but Descriptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Descriptions\",\n);\nexport const Divider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Divider() from the server but Divider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Divider\",\n);\nexport const Drawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Drawer() from the server but Drawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Drawer\",\n);\nexport const Dropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dropdown() from the server but Dropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Dropdown\",\n);\nexport const Empty = registerClientReference(\n    function() { throw new Error(\"Attempted to call Empty() from the server but Empty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Empty\",\n);\nexport const Flex = registerClientReference(\n    function() { throw new Error(\"Attempted to call Flex() from the server but Flex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Flex\",\n);\nexport const FloatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatButton() from the server but FloatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"FloatButton\",\n);\nexport const Form = registerClientReference(\n    function() { throw new Error(\"Attempted to call Form() from the server but Form is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Form\",\n);\nexport const Grid = registerClientReference(\n    function() { throw new Error(\"Attempted to call Grid() from the server but Grid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Grid\",\n);\nexport const Image = registerClientReference(\n    function() { throw new Error(\"Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Image\",\n);\nexport const Input = registerClientReference(\n    function() { throw new Error(\"Attempted to call Input() from the server but Input is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Input\",\n);\nexport const InputNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call InputNumber() from the server but InputNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"InputNumber\",\n);\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Layout\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"List\",\n);\nexport const Mentions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mentions() from the server but Mentions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Mentions\",\n);\nexport const Menu = registerClientReference(\n    function() { throw new Error(\"Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Menu\",\n);\nexport const Modal = registerClientReference(\n    function() { throw new Error(\"Attempted to call Modal() from the server but Modal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Modal\",\n);\nexport const Pagination = registerClientReference(\n    function() { throw new Error(\"Attempted to call Pagination() from the server but Pagination is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Pagination\",\n);\nexport const Popconfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popconfirm() from the server but Popconfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Popconfirm\",\n);\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Popover\",\n);\nexport const Progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call Progress() from the server but Progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Progress\",\n);\nexport const QRCode = registerClientReference(\n    function() { throw new Error(\"Attempted to call QRCode() from the server but QRCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"QRCode\",\n);\nexport const Radio = registerClientReference(\n    function() { throw new Error(\"Attempted to call Radio() from the server but Radio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Radio\",\n);\nexport const Rate = registerClientReference(\n    function() { throw new Error(\"Attempted to call Rate() from the server but Rate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Rate\",\n);\nexport const Result = registerClientReference(\n    function() { throw new Error(\"Attempted to call Result() from the server but Result is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Result\",\n);\nexport const Row = registerClientReference(\n    function() { throw new Error(\"Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Row\",\n);\nexport const Segmented = registerClientReference(\n    function() { throw new Error(\"Attempted to call Segmented() from the server but Segmented is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Segmented\",\n);\nexport const Select = registerClientReference(\n    function() { throw new Error(\"Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Select\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Skeleton\",\n);\nexport const Slider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Slider() from the server but Slider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Slider\",\n);\nexport const Space = registerClientReference(\n    function() { throw new Error(\"Attempted to call Space() from the server but Space is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Space\",\n);\nexport const Spin = registerClientReference(\n    function() { throw new Error(\"Attempted to call Spin() from the server but Spin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Spin\",\n);\nexport const Splitter = registerClientReference(\n    function() { throw new Error(\"Attempted to call Splitter() from the server but Splitter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Splitter\",\n);\nexport const Statistic = registerClientReference(\n    function() { throw new Error(\"Attempted to call Statistic() from the server but Statistic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Statistic\",\n);\nexport const Steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call Steps() from the server but Steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Steps\",\n);\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Switch\",\n);\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Table\",\n);\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tabs\",\n);\nexport const Tag = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tag() from the server but Tag is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tag\",\n);\nexport const TimePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimePicker() from the server but TimePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"TimePicker\",\n);\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Timeline\",\n);\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tooltip\",\n);\nexport const Tour = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tour() from the server but Tour is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tour\",\n);\nexport const Transfer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Transfer() from the server but Transfer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Transfer\",\n);\nexport const Tree = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tree() from the server but Tree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tree\",\n);\nexport const TreeSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TreeSelect() from the server but TreeSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"TreeSelect\",\n);\nexport const Typography = registerClientReference(\n    function() { throw new Error(\"Attempted to call Typography() from the server but Typography is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Typography\",\n);\nexport const Upload = registerClientReference(\n    function() { throw new Error(\"Attempted to call Upload() from the server but Upload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Upload\",\n);\nexport const Watermark = registerClientReference(\n    function() { throw new Error(\"Attempted to call Watermark() from the server but Watermark is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Watermark\",\n);\nexport const message = registerClientReference(\n    function() { throw new Error(\"Attempted to call message() from the server but message is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"message\",\n);\nexport const notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call notification() from the server but notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"notification\",\n);\nexport const theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call theme() from the server but theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"theme\",\n);\nexport const unstableSetRender = registerClientReference(\n    function() { throw new Error(\"Attempted to call unstableSetRender() from the server but unstableSetRender is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"unstableSetRender\",\n);\nexport const version = registerClientReference(\n    function() { throw new Error(\"Attempted to call version() from the server but version is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"version\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/interopRequireDefault.js"], "sourcesContent": ["function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,uBAAuB,CAAC;IAC/B,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAC7B,WAAW;IACb;AACF;AACA,OAAO,OAAO,GAAG,wBAAwB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/rc-pagination/lib/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar locale = {\n  // Options\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};\nvar _default = exports.default = locale;"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,SAAS;IACX,UAAU;IACV,gBAAgB;IAChB,SAAS;IACT,iBAAiB;IACjB,MAAM;IACN,aAAa;IACb,WAAW;IACX,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,WAAW;AACb;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/typeof.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,QAAQ,CAAC;IAChB;IAEA,OAAO,OAAO,OAAO,GAAG,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,CAAC;QAC/G,OAAO,OAAO;IAChB,IAAI,SAAU,CAAC;QACb,OAAO,KAAK,cAAc,OAAO,UAAU,EAAE,WAAW,KAAK,UAAU,MAAM,OAAO,SAAS,GAAG,WAAW,OAAO;IACpH,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,QAAQ;AAC3F;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/toPrimitive.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,yGAAsB,CAAC,UAAU;AAC/C,SAAS,YAAY,CAAC,EAAE,CAAC;IACvB,IAAI,YAAY,QAAQ,MAAM,CAAC,GAAG,OAAO;IACzC,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAC7B,IAAI,KAAK,MAAM,GAAG;QAChB,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QACvB,IAAI,YAAY,QAAQ,IAAI,OAAO;QACnC,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAC5C;AACA,OAAO,OAAO,GAAG,aAAa,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/toPropertyKey.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,yGAAsB,CAAC,UAAU;AAC/C,IAAI;AACJ,SAAS,cAAc,CAAC;IACtB,IAAI,IAAI,YAAY,GAAG;IACvB,OAAO,YAAY,QAAQ,KAAK,IAAI,IAAI;AAC1C;AACA,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/defineProperty.js"], "sourcesContent": ["var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,OAAO,CAAC,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAC/D,OAAO;QACP,YAAY,CAAC;QACb,cAAc,CAAC;QACf,UAAU,CAAC;IACb,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AACjB;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/%40babel/runtime/helpers/objectSpread2.js"], "sourcesContent": ["var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,QAAQ,CAAC,EAAE,CAAC;IACnB,IAAI,IAAI,OAAO,IAAI,CAAC;IACpB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,IAAI,OAAO,qBAAqB,CAAC;QACrC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAC5B,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QACzD,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IACvB;IACA,OAAO;AACT;AACA,SAAS,eAAe,CAAC;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAC/C,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAChD,eAAe,GAAG,GAAG,CAAC,CAAC,EAAE;QAC3B,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAC9I,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QACjE;IACF;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/rc-picker/lib/locale/common.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.commonLocale = void 0;\nvar commonLocale = exports.commonLocale = {\n  yearFormat: 'YYYY',\n  dayFormat: 'D',\n  cellMeridiemFormat: 'A',\n  monthBeforeYear: true\n};"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,YAAY,GAAG,KAAK;AAC5B,IAAI,eAAe,QAAQ,YAAY,GAAG;IACxC,YAAY;IACZ,WAAW;IACX,oBAAoB;IACpB,iBAAiB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/rc-picker/lib/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _common = require(\"./common\");\nvar locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {\n  locale: 'zh_CN',\n  today: '今天',\n  now: '此刻',\n  backToToday: '返回今天',\n  ok: '确定',\n  timeSelect: '选择时间',\n  dateSelect: '选择日期',\n  weekSelect: '选择周',\n  clear: '清除',\n  week: '周',\n  month: '月',\n  year: '年',\n  previousMonth: '上个月 (翻页上键)',\n  nextMonth: '下个月 (翻页下键)',\n  monthSelect: '选择月份',\n  yearSelect: '选择年份',\n  decadeSelect: '选择年代',\n  previousYear: '上一年 (Control键加左方向键)',\n  nextYear: '下一年 (Control键加右方向键)',\n  previousDecade: '上一年代',\n  nextDecade: '下一年代',\n  previousCentury: '上一世纪',\n  nextCentury: '下一世纪',\n  yearFormat: 'YYYY年',\n  cellDateFormat: 'D',\n  monthBeforeYear: false\n});\nvar _default = exports.default = locale;"], "names": [], "mappings": "AAAA;AAEA,IAAI,yBAAyB,yHAAwD,OAAO;AAC5F,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI,SAAS,CAAC,GAAG,eAAe,OAAO,EAAE,CAAC,GAAG,eAAe,OAAO,EAAE,CAAC,GAAG,QAAQ,YAAY,GAAG,CAAC,GAAG;IAClG,QAAQ;IACR,OAAO;IACP,KAAK;IACL,aAAa;IACb,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,eAAe;IACf,WAAW;IACX,aAAa;IACb,YAAY;IACZ,cAAc;IACd,cAAc;IACd,UAAU;IACV,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,aAAa;IACb,YAAY;IACZ,gBAAgB;IAChB,iBAAiB;AACnB;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/lib/time-picker/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst locale = {\n  placeholder: '请选择时间',\n  rangePlaceholder: ['开始时间', '结束时间']\n};\nvar _default = exports.default = locale;"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM,SAAS;IACb,aAAa;IACb,kBAAkB;QAAC;QAAQ;KAAO;AACpC;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/lib/date-picker/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-picker/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../../time-picker/locale/zh_CN\"));\n// 统一合并为完整的 Locale\nconst locale = {\n  lang: Object.assign({\n    placeholder: '请选择日期',\n    yearPlaceholder: '请选择年份',\n    quarterPlaceholder: '请选择季度',\n    monthPlaceholder: '请选择月份',\n    weekPlaceholder: '请选择周',\n    rangePlaceholder: ['开始日期', '结束日期'],\n    rangeYearPlaceholder: ['开始年份', '结束年份'],\n    rangeMonthPlaceholder: ['开始月份', '结束月份'],\n    rangeQuarterPlaceholder: ['开始季度', '结束季度'],\n    rangeWeekPlaceholder: ['开始周', '结束周']\n  }, _zh_CN.default),\n  timePickerLocale: Object.assign({}, _zh_CN2.default)\n};\n// should add whitespace between char in Button\nlocale.lang.ok = '确定';\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nvar _default = exports.default = locale;"], "names": [], "mappings": "AAAA;AAEA,IAAI,yBAAyB,yHAAwD,OAAO;AAC5F,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,kBAAkB;AAClB,MAAM,SAAS;IACb,MAAM,OAAO,MAAM,CAAC;QAClB,aAAa;QACb,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;YAAC;YAAQ;SAAO;QAClC,sBAAsB;YAAC;YAAQ;SAAO;QACtC,uBAAuB;YAAC;YAAQ;SAAO;QACvC,yBAAyB;YAAC;YAAQ;SAAO;QACzC,sBAAsB;YAAC;YAAO;SAAM;IACtC,GAAG,OAAO,OAAO;IACjB,kBAAkB,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ,OAAO;AACrD;AACA,+CAA+C;AAC/C,OAAO,IAAI,CAAC,EAAE,GAAG;AACjB,mBAAmB;AACnB,kGAAkG;AAClG,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/lib/calendar/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = exports.default = _zh_CN.default;"], "names": [], "mappings": "AAAA;AAEA,IAAI,yBAAyB,yHAAwD,OAAO;AAC5F,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,SAAS;AACb,IAAI,WAAW,QAAQ,OAAO,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/lib/locale/zh_CN.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-pagination/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../calendar/locale/zh_CN\"));\nvar _zh_CN3 = _interopRequireDefault(require(\"../date-picker/locale/zh_CN\"));\nvar _zh_CN4 = _interopRequireDefault(require(\"../time-picker/locale/zh_CN\"));\nconst typeTemplate = '${label}不是一个有效的${type}';\nconst localeValues = {\n  locale: 'zh-cn',\n  Pagination: _zh_CN.default,\n  DatePicker: _zh_CN3.default,\n  TimePicker: _zh_CN4.default,\n  Calendar: _zh_CN2.default,\n  // locales for all components\n  global: {\n    placeholder: '请选择',\n    close: '关闭'\n  },\n  Table: {\n    filterTitle: '筛选',\n    filterConfirm: '确定',\n    filterReset: '重置',\n    filterEmptyText: '无筛选项',\n    filterCheckAll: '全选',\n    filterSearchPlaceholder: '在筛选项中搜索',\n    emptyText: '暂无数据',\n    selectAll: '全选当页',\n    selectInvert: '反选当页',\n    selectNone: '清空所有',\n    selectionAll: '全选所有',\n    sortTitle: '排序',\n    expand: '展开行',\n    collapse: '关闭行',\n    triggerDesc: '点击降序',\n    triggerAsc: '点击升序',\n    cancelSort: '取消排序'\n  },\n  Modal: {\n    okText: '确定',\n    cancelText: '取消',\n    justOkText: '知道了'\n  },\n  Tour: {\n    Next: '下一步',\n    Previous: '上一步',\n    Finish: '结束导览'\n  },\n  Popconfirm: {\n    cancelText: '取消',\n    okText: '确定'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: '请输入搜索内容',\n    itemUnit: '项',\n    itemsUnit: '项',\n    remove: '删除',\n    selectCurrent: '全选当页',\n    removeCurrent: '删除当页',\n    selectAll: '全选所有',\n    deselectAll: '取消全选',\n    removeAll: '删除全部',\n    selectInvert: '反选当页'\n  },\n  Upload: {\n    uploading: '文件上传中',\n    removeFile: '删除文件',\n    uploadError: '上传错误',\n    previewFile: '预览文件',\n    downloadFile: '下载文件'\n  },\n  Empty: {\n    description: '暂无数据'\n  },\n  Icon: {\n    icon: '图标'\n  },\n  Text: {\n    edit: '编辑',\n    copy: '复制',\n    copied: '复制成功',\n    expand: '展开',\n    collapse: '收起'\n  },\n  Form: {\n    optional: '（可选）',\n    defaultValidateMessages: {\n      default: '字段验证错误${label}',\n      required: '请输入${label}',\n      enum: '${label}必须是其中一个[${enum}]',\n      whitespace: '${label}不能为空字符',\n      date: {\n        format: '${label}日期格式无效',\n        parse: '${label}不能转换为日期',\n        invalid: '${label}是一个无效日期'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label}须为${len}个字符',\n        min: '${label}最少${min}个字符',\n        max: '${label}最多${max}个字符',\n        range: '${label}须在${min}-${max}字符之间'\n      },\n      number: {\n        len: '${label}必须等于${len}',\n        min: '${label}最小值为${min}',\n        max: '${label}最大值为${max}',\n        range: '${label}须在${min}-${max}之间'\n      },\n      array: {\n        len: '须为${len}个${label}',\n        min: '最少${min}个${label}',\n        max: '最多${max}个${label}',\n        range: '${label}数量须在${min}-${max}之间'\n      },\n      pattern: {\n        mismatch: '${label}与模式不匹配${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: '预览'\n  },\n  QRCode: {\n    expired: '二维码过期',\n    refresh: '点击刷新',\n    scanned: '已扫描'\n  },\n  ColorPicker: {\n    presetEmpty: '暂无',\n    transparent: '无色',\n    singleColor: '单色',\n    gradientColor: '渐变色'\n  }\n};\nvar _default = exports.default = localeValues;"], "names": [], "mappings": "AAAA;AAEA,IAAI,yBAAyB,yHAAwD,OAAO;AAC5F,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,MAAM,eAAe;AACrB,MAAM,eAAe;IACnB,QAAQ;IACR,YAAY,OAAO,OAAO;IAC1B,YAAY,QAAQ,OAAO;IAC3B,YAAY,QAAQ,OAAO;IAC3B,UAAU,QAAQ,OAAO;IACzB,6BAA6B;IAC7B,QAAQ;QACN,aAAa;QACb,OAAO;IACT;IACA,OAAO;QACL,aAAa;QACb,eAAe;QACf,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,yBAAyB;QACzB,WAAW;QACX,WAAW;QACX,cAAc;QACd,YAAY;QACZ,cAAc;QACd,WAAW;QACX,QAAQ;QACR,UAAU;QACV,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA,OAAO;QACL,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA,YAAY;QACV,YAAY;QACZ,QAAQ;IACV;IACA,UAAU;QACR,QAAQ;YAAC;YAAI;SAAG;QAChB,mBAAmB;QACnB,UAAU;QACV,WAAW;QACX,QAAQ;QACR,eAAe;QACf,eAAe;QACf,WAAW;QACX,aAAa;QACb,WAAW;QACX,cAAc;IAChB;IACA,QAAQ;QACN,WAAW;QACX,YAAY;QACZ,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IACA,OAAO;QACL,aAAa;IACf;IACA,MAAM;QACJ,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA,MAAM;QACJ,UAAU;QACV,yBAAyB;YACvB,SAAS;YACT,UAAU;YACV,MAAM;YACN,YAAY;YACZ,MAAM;gBACJ,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;YACA,OAAO;gBACL,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,KAAK;gBACL,KAAK;YACP;YACA,QAAQ;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,OAAO;YACT;YACA,QAAQ;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,OAAO;YACT;YACA,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,OAAO;YACT;YACA,SAAS;gBACP,UAAU;YACZ;QACF;IACF;IACA,OAAO;QACL,SAAS;IACX;IACA,QAAQ;QACN,SAAS;QACT,SAAS;QACT,SAAS;IACX;IACA,aAAa;QACX,aAAa;QACb,aAAa;QACb,aAAa;QACb,eAAe;IACjB;AACF;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/node_modules/antd/locale/zh_CN.js"], "sourcesContent": ["module.exports = require('../lib/locale/zh_CN');"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}