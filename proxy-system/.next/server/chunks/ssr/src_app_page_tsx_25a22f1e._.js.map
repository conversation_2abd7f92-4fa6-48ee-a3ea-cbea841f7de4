{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { Button, Typography, Card, Row, Col, Space } from 'antd';\nimport { ShoppingCartOutlined, UserOutlined, DashboardOutlined } from '@ant-design/icons';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/AuthProvider';\n\nconst { Title, Paragraph } = Typography;\n\nexport default function Home() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    // 如果用户已登录，重定向到仪表板\n    if (user && !loading) {\n      router.push('/dashboard');\n    }\n  }, [user, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div>加载中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Title level={3} className=\"!mb-0\">代理系统</Title>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {!user ? (\n                <>\n                  <Link href=\"/auth/login\">\n                    <Button type=\"text\" icon={<UserOutlined />}>\n                      登录\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth/register\">\n                    <Button type=\"primary\">\n                      注册\n                    </Button>\n                  </Link>\n                </>\n              ) : (\n                <Link href=\"/dashboard\">\n                  <Button type=\"primary\" icon={<DashboardOutlined />}>\n                    控制台\n                  </Button>\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <Title level={1} className=\"!text-4xl !font-bold !text-gray-900\">\n            专业的代理服务平台\n          </Title>\n          <Paragraph className=\"!text-xl !text-gray-600 !mt-4 !max-w-3xl !mx-auto\">\n            基于PX6.me的高质量代理服务，提供IPv4、IPv6和共享代理，支持HTTP和SOCKS5协议，\n            覆盖全球多个国家和地区，为您的业务提供稳定可靠的网络代理解决方案。\n          </Paragraph>\n\n          <div className=\"mt-8\">\n            <Space size=\"large\">\n              <Link href=\"/auth/register\">\n                <Button type=\"primary\" size=\"large\" icon={<ShoppingCartOutlined />}>\n                  立即开始\n                </Button>\n              </Link>\n              <Link href=\"/auth/login\">\n                <Button size=\"large\">\n                  已有账户\n                </Button>\n              </Link>\n            </Space>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAExB,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;QAClB,IAAI,QAAQ,CAAC,SAAS;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;0BAAI;;;;;;;;;;;IAGX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAQ;;;;;;;;;;;0CAErC,8OAAC;gCAAI,WAAU;0CACZ,CAAC,qBACA;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;0DAAK;;;;;;;;;;;sDAI9C,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kMAAA,CAAA,SAAM;gDAAC,MAAK;0DAAU;;;;;;;;;;;;iEAM3B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhE,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAsC;;;;;;sCAGjE,8OAAC;4BAAU,WAAU;sCAAoD;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gMAAA,CAAA,QAAK;gCAAC,MAAK;;kDACV,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAU,MAAK;4CAAQ,oBAAM,8OAAC,kOAAA,CAAA,uBAAoB;;;;;sDAAK;;;;;;;;;;;kDAItE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC", "debugId": null}}]}