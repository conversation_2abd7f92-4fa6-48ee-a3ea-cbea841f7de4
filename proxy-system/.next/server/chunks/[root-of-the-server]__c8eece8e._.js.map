{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/lib/db.ts"], "sourcesContent": ["import mysql from 'mysql2/promise';\n\n// 数据库连接配置\nconst dbConfig = {\n  host: process.env.DB_HOST || 'localhost',\n  port: parseInt(process.env.DB_PORT || '3306'),\n  user: process.env.DB_USER || 'root',\n  password: process.env.DB_PASSWORD || '',\n  database: process.env.DB_NAME || 'proxy_system',\n  waitForConnections: true,\n  connectionLimit: 10,\n  queueLimit: 0,\n  charset: 'utf8mb4',\n  timezone: '+08:00'\n};\n\n// 创建连接池\nconst pool = mysql.createPool(dbConfig);\n\n// 数据库连接函数\nexport async function getConnection() {\n  try {\n    const connection = await pool.getConnection();\n    return connection;\n  } catch (error) {\n    console.error('数据库连接失败:', error);\n    throw error;\n  }\n}\n\n// 执行查询\nexport async function query(sql: string, params?: any[]) {\n  const connection = await getConnection();\n  try {\n    const [results] = await connection.execute(sql, params);\n    return results;\n  } catch (error) {\n    console.error('数据库查询失败:', error);\n    throw error;\n  } finally {\n    connection.release();\n  }\n}\n\n// 执行事务\nexport async function transaction(callback: (connection: any) => Promise<any>) {\n  const connection = await getConnection();\n  try {\n    await connection.beginTransaction();\n    const result = await callback(connection);\n    await connection.commit();\n    return result;\n  } catch (error) {\n    await connection.rollback();\n    console.error('事务执行失败:', error);\n    throw error;\n  } finally {\n    connection.release();\n  }\n}\n\n// 初始化数据库表\nexport async function initDatabase() {\n  try {\n    // 创建用户表\n    await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        username VARCHAR(100) NOT NULL,\n        password VARCHAR(255) NOT NULL,\n        balance DECIMAL(10,2) DEFAULT 0.00,\n        role ENUM('admin', 'user') DEFAULT 'user',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        INDEX idx_email (email),\n        INDEX idx_role (role)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n    `);\n\n    // 创建订单表\n    await query(`\n      CREATE TABLE IF NOT EXISTS orders (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        user_id INT NOT NULL,\n        proxy_count INT NOT NULL,\n        period INT NOT NULL,\n        country VARCHAR(10) NOT NULL,\n        version TINYINT NOT NULL DEFAULT 6,\n        type ENUM('http', 'socks') DEFAULT 'http',\n        price DECIMAL(10,2) NOT NULL,\n        status ENUM('pending', 'paid', 'completed', 'failed') DEFAULT 'pending',\n        px6_order_data TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n        INDEX idx_user_id (user_id),\n        INDEX idx_status (status),\n        INDEX idx_created_at (created_at)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n    `);\n\n    // 创建代理表\n    await query(`\n      CREATE TABLE IF NOT EXISTS proxies (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        user_id INT NOT NULL,\n        order_id INT NOT NULL,\n        px6_proxy_id VARCHAR(50) NOT NULL,\n        ip VARCHAR(45) NOT NULL,\n        host VARCHAR(45) NOT NULL,\n        port VARCHAR(10) NOT NULL,\n        username VARCHAR(50) NOT NULL,\n        password VARCHAR(50) NOT NULL,\n        type ENUM('http', 'socks') DEFAULT 'http',\n        country VARCHAR(10) NOT NULL,\n        date_start TIMESTAMP NOT NULL,\n        date_end TIMESTAMP NOT NULL,\n        description TEXT,\n        active TINYINT(1) DEFAULT 1,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,\n        INDEX idx_user_id (user_id),\n        INDEX idx_order_id (order_id),\n        INDEX idx_px6_proxy_id (px6_proxy_id),\n        INDEX idx_active (active),\n        INDEX idx_date_end (date_end)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n    `);\n\n    // 创建支付记录表\n    await query(`\n      CREATE TABLE IF NOT EXISTS payments (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        user_id INT NOT NULL,\n        order_id INT,\n        amount DECIMAL(10,2) NOT NULL,\n        type ENUM('recharge', 'purchase', 'refund') NOT NULL,\n        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',\n        payment_method VARCHAR(50),\n        transaction_id VARCHAR(100),\n        description TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,\n        INDEX idx_user_id (user_id),\n        INDEX idx_order_id (order_id),\n        INDEX idx_status (status),\n        INDEX idx_type (type)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n    `);\n\n    // 创建系统配置表\n    await query(`\n      CREATE TABLE IF NOT EXISTS system_config (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        config_key VARCHAR(100) UNIQUE NOT NULL,\n        config_value TEXT,\n        description TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        INDEX idx_config_key (config_key)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n    `);\n\n    console.log('数据库表初始化完成');\n  } catch (error) {\n    console.error('数据库初始化失败:', error);\n    throw error;\n  }\n}\n\nexport default pool;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,UAAU;AACV,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;IAC7B,MAAM,SAAS,QAAQ,GAAG,CAAC,OAAO,IAAI;IACtC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;IAC7B,UAAU,QAAQ,GAAG,CAAC,WAAW,IAAI;IACrC,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;IACjC,oBAAoB;IACpB,iBAAiB;IACjB,YAAY;IACZ,SAAS;IACT,UAAU;AACZ;AAEA,QAAQ;AACR,MAAM,OAAO,mIAAA,CAAA,UAAK,CAAC,UAAU,CAAC;AAGvB,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,MAAM,KAAK,aAAa;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM;IACR;AACF;AAGO,eAAe,MAAM,GAAW,EAAE,MAAc;IACrD,MAAM,aAAa,MAAM;IACzB,IAAI;QACF,MAAM,CAAC,QAAQ,GAAG,MAAM,WAAW,OAAO,CAAC,KAAK;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM;IACR,SAAU;QACR,WAAW,OAAO;IACpB;AACF;AAGO,eAAe,YAAY,QAA2C;IAC3E,MAAM,aAAa,MAAM;IACzB,IAAI;QACF,MAAM,WAAW,gBAAgB;QACjC,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,WAAW,MAAM;QACvB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,WAAW,QAAQ;QACzB,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM;IACR,SAAU;QACR,WAAW,OAAO;IACpB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ;QACR,MAAM,MAAM,CAAC;;;;;;;;;;;;;IAab,CAAC;QAED,QAAQ;QACR,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;IAmBb,CAAC;QAED,QAAQ;QACR,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2Bb,CAAC;QAED,UAAU;QACV,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;IAoBb,CAAC;QAED,UAAU;QACV,MAAM,MAAM,CAAC;;;;;;;;;;IAUb,CAAC;QAED,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/lib/utils.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { JWTPayload } from '@/types';\n\n// 密码加密\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 10;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// 密码验证\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// 生成JWT Token\nexport function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT_SECRET environment variable is not set');\n  }\n  \n  return jwt.sign(payload, secret, {\n    expiresIn: '7d', // 7天过期\n  });\n}\n\n// 验证JWT Token\nexport function verifyToken(token: string): JWTPayload {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT_SECRET environment variable is not set');\n  }\n  \n  try {\n    return jwt.verify(token, secret) as JWTPayload;\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n}\n\n// 格式化价格\nexport function formatPrice(price: number, currency: string = 'CNY'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 2,\n  }).format(price);\n}\n\n// 格式化日期\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n  }).format(d);\n}\n\n// 计算剩余时间\nexport function getTimeRemaining(endDate: Date | string): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n  total: number;\n} {\n  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;\n  const now = new Date();\n  const total = end.getTime() - now.getTime();\n  \n  if (total <= 0) {\n    return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };\n  }\n  \n  const days = Math.floor(total / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((total % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((total % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((total % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds, total };\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number = 32): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 验证密码强度\nexport function isValidPassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('密码长度至少8位');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('密码必须包含至少一个大写字母');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('密码必须包含至少一个小写字母');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('密码必须包含至少一个数字');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\n// 计算加价后的价格\nexport function calculateMarkupPrice(originalPrice: number, markupRate: number = 1.2): number {\n  return Math.ceil(originalPrice * markupRate * 100) / 100; // 向上取整到分\n}\n\n// 代理状态文本映射\nexport const PROXY_STATUS_TEXT = {\n  '1': '活跃',\n  '0': '已过期'\n};\n\n// 订单状态文本映射\nexport const ORDER_STATUS_TEXT = {\n  'pending': '待支付',\n  'paid': '已支付',\n  'completed': '已完成',\n  'failed': '失败'\n};\n\n// 支付类型文本映射\nexport const PAYMENT_TYPE_TEXT = {\n  'recharge': '充值',\n  'purchase': '购买',\n  'refund': '退款'\n};\n\n// 支付状态文本映射\nexport const PAYMENT_STATUS_TEXT = {\n  'pending': '处理中',\n  'completed': '已完成',\n  'failed': '失败'\n};\n\n// 代理类型文本映射\nexport const PROXY_TYPE_TEXT = {\n  'http': 'HTTP/HTTPS',\n  'socks': 'SOCKS5'\n};\n\n// 代理版本文本映射\nexport const PROXY_VERSION_TEXT = {\n  4: 'IPv4',\n  3: 'IPv4 共享',\n  6: 'IPv6'\n};\n\n// 错误处理\nexport class AppError extends Error {\n  public statusCode: number;\n  public isOperational: boolean;\n\n  constructor(message: string, statusCode: number = 500) {\n    super(message);\n    this.statusCode = statusCode;\n    this.isOperational = true;\n\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\n// API响应包装器\nexport function createApiResponse<T>(\n  success: boolean,\n  data?: T,\n  message?: string,\n  error?: string\n) {\n  return {\n    success,\n    data,\n    message,\n    error,\n    timestamp: new Date().toISOString()\n  };\n}\n\n// 分页计算\nexport function calculatePagination(\n  page: number,\n  limit: number,\n  total: number\n) {\n  const offset = (page - 1) * limit;\n  const totalPages = Math.ceil(total / limit);\n  const hasNext = page < totalPages;\n  const hasPrev = page > 1;\n\n  return {\n    page,\n    limit,\n    offset,\n    total,\n    totalPages,\n    hasNext,\n    hasPrev\n  };\n}\n\n// 延迟函数\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// 重试函数\nexport async function retry<T>(\n  fn: () => Promise<T>,\n  maxAttempts: number = 3,\n  delayMs: number = 1000\n): Promise<T> {\n  let lastError: Error;\n  \n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      return await fn();\n    } catch (error) {\n      lastError = error as Error;\n      \n      if (attempt === maxAttempts) {\n        throw lastError;\n      }\n      \n      await delay(delayMs * attempt); // 指数退避\n    }\n  }\n  \n  throw lastError!;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAIO,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAwC;IACpE,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,QAAQ;QAC/B,WAAW;IACb;AACF;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,YAAY,KAAa,EAAE,WAAmB,KAAK;IACjE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,iBAAiB,OAAsB;IAOrD,MAAM,MAAM,OAAO,YAAY,WAAW,IAAI,KAAK,WAAW;IAC9D,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,OAAO,KAAK,IAAI,OAAO;IAEzC,IAAI,SAAS,GAAG;QACd,OAAO;YAAE,MAAM;YAAG,OAAO;YAAG,SAAS;YAAG,SAAS;YAAG,OAAO;QAAE;IAC/D;IAEA,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;IACpD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IAC1E,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IAClE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,EAAE,IAAK;IAEnD,OAAO;QAAE;QAAM;QAAO;QAAS;QAAS;IAAM;AAChD;AAGO,SAAS,qBAAqB,SAAiB,EAAE;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,gBAAgB,QAAgB;IAI9C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,qBAAqB,aAAqB,EAAE,aAAqB,GAAG;IAClF,OAAO,KAAK,IAAI,CAAC,gBAAgB,aAAa,OAAO,KAAK,SAAS;AACrE;AAGO,MAAM,oBAAoB;IAC/B,KAAK;IACL,KAAK;AACP;AAGO,MAAM,oBAAoB;IAC/B,WAAW;IACX,QAAQ;IACR,aAAa;IACb,UAAU;AACZ;AAGO,MAAM,oBAAoB;IAC/B,YAAY;IACZ,YAAY;IACZ,UAAU;AACZ;AAGO,MAAM,sBAAsB;IACjC,WAAW;IACX,aAAa;IACb,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,qBAAqB;IAChC,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAGO,MAAM,iBAAiB;IACrB,WAAmB;IACnB,cAAuB;IAE9B,YAAY,OAAe,EAAE,aAAqB,GAAG,CAAE;QACrD,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QAErB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAChD;AACF;AAGO,SAAS,kBACd,OAAgB,EAChB,IAAQ,EACR,OAAgB,EAChB,KAAc;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAGO,SAAS,oBACd,IAAY,EACZ,KAAa,EACb,KAAa;IAEb,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IACrC,MAAM,UAAU,OAAO;IACvB,MAAM,UAAU,OAAO;IAEvB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,eAAe,MACpB,EAAoB,EACpB,cAAsB,CAAC,EACvB,UAAkB,IAAI;IAEtB,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,IAAI,YAAY,aAAa;gBAC3B,MAAM;YACR;YAEA,MAAM,MAAM,UAAU,UAAU,OAAO;QACzC;IACF;IAEA,MAAM;AACR", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/api/auth/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { query } from '@/lib/db';\nimport { verifyToken, createApiResponse } from '@/lib/utils';\nimport { User } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // 从cookie或Authorization header获取token\n    const token = request.cookies.get('token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '');\n\n    if (!token) {\n      return NextResponse.json(\n        createApiResponse(false, null, null, '未提供认证令牌'),\n        { status: 401 }\n      );\n    }\n\n    // 验证token\n    let payload;\n    try {\n      payload = verifyToken(token);\n    } catch (error) {\n      return NextResponse.json(\n        createApiResponse(false, null, null, '无效的认证令牌'),\n        { status: 401 }\n      );\n    }\n\n    // 查询用户信息\n    const users = await query(\n      'SELECT id, email, username, balance, role, created_at FROM users WHERE id = ?',\n      [payload.userId]\n    ) as User[];\n\n    if (users.length === 0) {\n      return NextResponse.json(\n        createApiResponse(false, null, null, '用户不存在'),\n        { status: 404 }\n      );\n    }\n\n    const user = users[0];\n\n    return NextResponse.json(\n      createApiResponse(true, user, '获取用户信息成功')\n    );\n\n  } catch (error) {\n    console.error('Get user info error:', error);\n    return NextResponse.json(\n      createApiResponse(false, null, null, '服务器内部错误'),\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,sCAAsC;QACtC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU,SAC9B,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,MAAM,YACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI;QACJ,IAAI;YACF,UAAU,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,MAAM,YACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,QAAQ,MAAM,CAAA,GAAA,kHAAA,CAAA,QAAK,AAAD,EACtB,iFACA;YAAC,QAAQ,MAAM;SAAC;QAGlB,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,MAAM,UACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,MAAM;IAGlC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM,MAAM,YACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}