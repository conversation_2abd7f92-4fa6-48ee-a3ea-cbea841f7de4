{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Spin, Typography } from 'antd';\nimport { GlobalOutlined } from '@ant-design/icons';\n\nconst { Text } = Typography;\n\ninterface LoadingScreenProps {\n  message?: string;\n}\n\nexport default function LoadingScreen({ message = \"系统初始化中...\" }: LoadingScreenProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"text-center\">\n        {/* Logo动画 */}\n        <div className=\"mb-8\">\n          <div className=\"relative\">\n            <div className=\"w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto animate-pulse\">\n              <GlobalOutlined className=\"text-white text-3xl\" />\n            </div>\n            {/* 环形加载动画 */}\n            <div className=\"absolute inset-0 w-20 h-20 mx-auto\">\n              <div className=\"w-full h-full border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin\"></div>\n            </div>\n          </div>\n        </div>\n        \n        {/* 品牌名称 */}\n        <div className=\"mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">ProxyHub</h1>\n          <Text className=\"text-gray-600\">专业的代理服务平台</Text>\n        </div>\n        \n        {/* 加载指示器 */}\n        <div className=\"mb-4\">\n          <Spin size=\"large\" />\n        </div>\n        \n        {/* 加载消息 */}\n        <Text className=\"text-gray-600 text-lg\">{message}</Text>\n        \n        {/* 加载进度条动画 */}\n        <div className=\"mt-6 w-64 mx-auto\">\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <div className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full animate-pulse\" style={{ width: '60%' }}></div>\n          </div>\n        </div>\n        \n        {/* 提示文字 */}\n        <div className=\"mt-8 max-w-md mx-auto\">\n          <Text className=\"text-gray-500 text-sm\">\n            正在为您准备最佳的代理服务体验...\n          </Text>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMZ,SAAS,cAAc,EAAE,UAAU,WAAW,EAAsB;IACjF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;0CAG5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAMrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;8BAIlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;wBAAC,MAAK;;;;;;;;;;;8BAIb,6LAAC;oBAAK,WAAU;8BAAyB;;;;;;8BAGzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAA8E,OAAO;gCAAE,OAAO;4BAAM;;;;;;;;;;;;;;;;8BAKvH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAOlD;KA/CwB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Typography, message, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/AuthProvider';\nimport LoadingScreen from '@/components/LoadingScreen';\n\nconst { Title, Text } = Typography;\n\nexport default function LoginPage() {\n  const [loading, setLoading] = useState(false);\n  const { login, loading: authLoading } = useAuth();\n  const router = useRouter();\n\n  if (authLoading) {\n    return <LoadingScreen message=\"正在验证用户状态...\" />;\n  }\n\n  const onFinish = async (values: { email: string; password: string }) => {\n    setLoading(true);\n    try {\n      await login(values.email, values.password);\n      message.success('登录成功');\n      router.push('/dashboard');\n    } catch (error) {\n      message.error(error instanceof Error ? error.message : '登录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <Title level={2}>代理系统</Title>\n          <Text type=\"secondary\">登录到您的账户</Text>\n        </div>\n        \n        <Card>\n          <Form\n            name=\"login\"\n            onFinish={onFinish}\n            autoComplete=\"off\"\n            layout=\"vertical\"\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"email\"\n              label=\"邮箱\"\n              rules={[\n                { required: true, message: '请输入邮箱' },\n                { type: 'email', message: '请输入有效的邮箱地址' }\n              ]}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"请输入邮箱\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[{ required: true, message: '请输入密码' }]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请输入密码\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                block\n              >\n                登录\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <Divider>或</Divider>\n\n          <div className=\"text-center\">\n            <Text>\n              还没有账户？{' '}\n              <Link href=\"/auth/register\" className=\"text-blue-600 hover:text-blue-500\">\n                立即注册\n              </Link>\n            </Text>\n          </div>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAEnB,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,IAAI,aAAa;QACf,qBAAO,6LAAC,sIAAA,CAAA,UAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,OAAO,KAAK,EAAE,OAAO,QAAQ;YACzC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,6LAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;8BAGzB,6LAAC,iLAAA,CAAA,OAAI;;sCACH,6LAAC,iLAAA,CAAA,OAAI;4BACH,MAAK;4BACL,UAAU;4BACV,cAAa;4BACb,QAAO;4BACP,MAAK;;8CAEL,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAQ;wCACnC;4CAAE,MAAM;4CAAS,SAAS;wCAAa;qCACxC;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAQ;qCAAE;8CAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;8CACR,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,KAAK;kDACN;;;;;;;;;;;;;;;;;sCAML,6LAAC,uLAAA,CAAA,UAAO;sCAAC;;;;;;sCAET,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;oCAAK;oCACG;kDACP,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxF;GAzFwB;;QAEkB,qIAAA,CAAA,UAAO;QAChC,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}