{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Button } from 'antd';\nimport { UserOutlined, DashboardOutlined } from '@ant-design/icons';\nimport { Globe } from 'lucide-react';\nimport Link from 'next/link';\nimport { useAuth } from '@/components/AuthProvider';\n\nconst { Header } = Layout;\nconst { Title } = Typography;\n\ninterface AppHeaderProps {\n  currentPage?: 'home' | 'products' | 'pricing' | 'support';\n}\n\nexport default function AppHeader({ currentPage = 'home' }: AppHeaderProps) {\n  const { user } = useAuth();\n\n  const getLinkClass = (page: string) => {\n    return currentPage === page \n      ? \"text-blue-600 font-medium\" \n      : \"text-gray-600 hover:text-gray-900 transition-colors\";\n  };\n\n  return (\n    <Header className=\"bg-white shadow-sm px-0\" style={{ backgroundColor: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16 items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n              <Globe className=\"text-white\" size={18} />\n            </div>\n            <Title level={3} className=\"!mb-0\" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>\n          </Link>\n          \n          <div className=\"flex items-center space-x-6\">\n            <Link href=\"/\" className={getLinkClass('home')}>\n              首页\n            </Link>\n            <Link href=\"/products\" className={getLinkClass('products')}>\n              产品服务\n            </Link>\n            <Link href=\"/pricing\" className={getLinkClass('pricing')}>\n              价格方案\n            </Link>\n            <Link href=\"/support\" className={getLinkClass('support')}>\n              技术支持\n            </Link>\n            \n            <div className=\"flex items-center space-x-4 ml-4\">\n              {!user ? (\n                <>\n                  <Link href=\"/auth/login\">\n                    <Button type=\"text\" icon={<UserOutlined />} style={{ color: '#374151' }}>\n                      登录\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth/register\">\n                    <Button \n                      type=\"primary\" \n                      className=\"bg-gradient-to-r from-blue-500 to-indigo-600 border-0\"\n                      style={{ background: 'linear-gradient(to right, #3b82f6, #4f46e5)', border: 'none' }}\n                    >\n                      免费注册\n                    </Button>\n                  </Link>\n                </>\n              ) : (\n                <Link href=\"/dashboard\">\n                  <Button type=\"primary\" icon={<DashboardOutlined />}>\n                    控制台\n                  </Button>\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </Header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMb,SAAS,UAAU,EAAE,cAAc,MAAM,EAAkB;;IACxE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,OAAO,gBAAgB,OACnB,8BACA;IACN;IAEA,qBACE,6LAAC;QAAO,WAAU;QAA0B,OAAO;YAAE,iBAAiB;QAAQ;kBAC5E,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,MAAM;;;;;;;;;;;0CAEtC,6LAAC;gCAAM,OAAO;gCAAG,WAAU;gCAAQ,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAE;0CAAG;;;;;;;;;;;;kCAG7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAW,aAAa;0CAAS;;;;;;0CAGhD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAW,aAAa;0CAAa;;;;;;0CAG5D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAG1D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;0CACZ,CAAC,qBACA;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDAAK,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;sDAI3E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,YAAY;oDAA+C,QAAQ;gDAAO;0DACpF;;;;;;;;;;;;iEAML,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtE;GAjEwB;;QACL,qIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppFooter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Row, Col, Divider } from 'antd';\nimport { Globe, Users, Clock } from 'lucide-react';\nimport Link from 'next/link';\n\nconst { Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\n\nexport default function AppFooter() {\n  return (\n    <Footer className=\"bg-gray-900 text-white\" style={{ backgroundColor: '#111827', color: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <Row gutter={[48, 32]}>\n          <Col xs={24} sm={12} lg={6}>\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <Globe className=\"text-white\" size={18} />\n              </div>\n              <Title level={4} className=\"!mb-0\" style={{ color: 'white', margin: 0 }}>ProxyHub</Title>\n            </div>\n            <Paragraph className=\"!mb-6\" style={{ color: '#9ca3af', marginBottom: '1.5rem' }}>\n              专业的代理服务平台，为您的业务提供稳定可靠的网络代理解决方案。\n            </Paragraph>\n            <div className=\"flex space-x-4\">\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Globe className=\"text-gray-400\" size={20} />\n              </div>\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Users className=\"text-gray-400\" size={20} />\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>产品服务</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv4 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv6 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  共享代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/pricing\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  企业定制\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>支持中心</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  使用文档\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  API 接口\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  常见问题\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  技术支持\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>联系我们</Title>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"text-gray-400\" size={16} />\n                <Text style={{ color: '#9ca3af' }}>7×24小时服务</Text>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  在线客服\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  工单系统\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  商务合作\n                </Link>\n              </div>\n            </div>\n          </Col>\n        </Row>\n        \n        <Divider style={{ borderColor: '#374151', margin: '2rem 0' }} />\n        \n        <div className=\"flex flex-col md:flex-row justify-between items-center\">\n          <Text style={{ color: '#9ca3af' }}>\n            © 2024 ProxyHub. 保留所有权利.\n          </Text>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              隐私政策\n            </Link>\n            <Link href=\"/terms\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              服务条款\n            </Link>\n            <Link href=\"/legal\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              法律声明\n            </Link>\n          </div>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;QAAyB,OAAO;YAAE,iBAAiB;YAAW,OAAO;QAAQ;kBAC7F,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCACnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAA<PERSON>,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;sDAEtC,6LAAC;4CAAM,OAAO;4CAAG,WAAU;4CAAQ,OAAO;gDAAE,OAAO;gDAAS,QAAQ;4CAAE;sDAAG;;;;;;;;;;;;8CAE3E,6LAAC;oCAAU,WAAU;oCAAQ,OAAO;wCAAE,OAAO;wCAAW,cAAc;oCAAS;8CAAG;;;;;;8CAGlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAK7C,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAgB,MAAM;;;;;;8DACvC,6LAAC;oDAAK,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAErC,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1G,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,aAAa;wBAAW,QAAQ;oBAAS;;;;;;8BAE3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAGnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGlG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGhG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5G;KA5HwB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/support/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { \n  Layout, \n  Typography, \n  Card, \n  Row, \n  Col, \n  Button, \n  Form,\n  Input,\n  Select,\n  message,\n  Collapse,\n  Space,\n  Tag\n} from 'antd';\nimport { \n  Globe,\n  MessageCircle,\n  Mail,\n  Phone,\n  Clock,\n  HelpCircle,\n  FileText,\n  Users\n} from 'lucide-react';\nimport Link from 'next/link';\nimport AppHeader from '@/components/AppHeader';\nimport AppFooter from '@/components/AppFooter';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Panel } = Collapse;\n\nexport default function SupportPage() {\n  const [form] = Form.useForm();\n\n  const onFinish = (values: any) => {\n    console.log('Support form submitted:', values);\n    message.success('您的问题已提交，我们会尽快回复您！');\n    form.resetFields();\n  };\n\n  const faqData = [\n    {\n      key: '1',\n      question: '如何开始使用代理服务？',\n      answer: '注册账户后，选择适合的套餐，完成支付即可获得代理配置信息。我们提供详细的配置教程和技术支持。'\n    },\n    {\n      key: '2',\n      question: '代理服务支持哪些协议？',\n      answer: '我们支持HTTP、HTTPS和SOCKS5协议，可以满足各种应用场景的需求。'\n    },\n    {\n      key: '3',\n      question: '如何配置代理？',\n      answer: '购买后您会收到包含IP地址、端口、用户名和密码的配置信息。可以在浏览器、应用程序或代码中配置使用。'\n    },\n    {\n      key: '4',\n      question: '代理IP会变化吗？',\n      answer: '独享代理的IP地址在服务期内保持不变。共享代理的IP可能会定期轮换以保证服务质量。'\n    },\n    {\n      key: '5',\n      question: '支持退款吗？',\n      answer: '我们提供7天无理由退款保证。如果您对服务不满意，可以在购买后7天内申请全额退款。'\n    },\n    {\n      key: '6',\n      question: '如何查看使用统计？',\n      answer: '登录用户面板即可查看详细的使用统计，包括流量使用、连接次数、在线时间等信息。'\n    }\n  ];\n\n  return (\n    <Layout className=\"min-h-screen\">\n      <AppHeader currentPage=\"support\" />\n\n      <Content>\n        {/* 页面标题 */}\n        <section className=\"bg-gradient-to-r from-blue-50 to-indigo-50 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={1} className=\"!text-4xl !font-bold !text-gray-900 !mb-4\">\n              技术支持\n            </Title>\n            <Paragraph className=\"!text-xl !text-gray-600 !max-w-3xl !mx-auto\">\n              我们提供全方位的技术支持服务，帮助您快速解决问题，\n              确保代理服务的稳定运行。\n            </Paragraph>\n          </div>\n        </section>\n\n        {/* 支持方式 */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <Title level={2} className=\"!text-3xl !font-bold !text-gray-900\">\n                联系我们\n              </Title>\n              <Paragraph className=\"!text-lg !text-gray-600\">\n                选择最适合您的联系方式\n              </Paragraph>\n            </div>\n            \n            <Row gutter={[32, 32]}>\n              <Col xs={24} md={8}>\n                <Card className=\"text-center h-full shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <MessageCircle className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>在线客服</Title>\n                  <Paragraph className=\"text-gray-600 mb-6\">\n                    7×24小时在线客服，实时解答您的问题\n                  </Paragraph>\n                  <Button type=\"primary\" size=\"large\" block>\n                    开始对话\n                  </Button>\n                  <div className=\"mt-4\">\n                    <Tag color=\"green\">平均响应时间: 2分钟</Tag>\n                  </div>\n                </Card>\n              </Col>\n              \n              <Col xs={24} md={8}>\n                <Card className=\"text-center h-full shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <Mail className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>邮件支持</Title>\n                  <Paragraph className=\"text-gray-600 mb-6\">\n                    发送邮件给我们，获得详细的技术支持\n                  </Paragraph>\n                  <Button size=\"large\" block>\n                    发送邮件\n                  </Button>\n                  <div className=\"mt-4\">\n                    <Tag color=\"blue\">平均响应时间: 4小时</Tag>\n                  </div>\n                </Card>\n              </Col>\n              \n              <Col xs={24} md={8}>\n                <Card className=\"text-center h-full shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <Phone className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>电话支持</Title>\n                  <Paragraph className=\"text-gray-600 mb-6\">\n                    企业用户专享电话技术支持服务\n                  </Paragraph>\n                  <Button size=\"large\" block>\n                    预约通话\n                  </Button>\n                  <div className=\"mt-4\">\n                    <Tag color=\"orange\">仅限企业用户</Tag>\n                  </div>\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        </section>\n\n        {/* 提交工单 */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <Title level={2} className=\"!text-3xl !font-bold !text-gray-900\">\n                提交工单\n              </Title>\n              <Paragraph className=\"!text-lg !text-gray-600\">\n                详细描述您遇到的问题，我们会尽快为您解决\n              </Paragraph>\n            </div>\n            \n            <Card className=\"shadow-lg\">\n              <Form\n                form={form}\n                layout=\"vertical\"\n                onFinish={onFinish}\n                size=\"large\"\n              >\n                <Row gutter={16}>\n                  <Col xs={24} md={12}>\n                    <Form.Item\n                      name=\"name\"\n                      label=\"姓名\"\n                      rules={[{ required: true, message: '请输入您的姓名' }]}\n                    >\n                      <Input placeholder=\"请输入您的姓名\" />\n                    </Form.Item>\n                  </Col>\n                  <Col xs={24} md={12}>\n                    <Form.Item\n                      name=\"email\"\n                      label=\"邮箱\"\n                      rules={[\n                        { required: true, message: '请输入邮箱' },\n                        { type: 'email', message: '请输入有效的邮箱地址' }\n                      ]}\n                    >\n                      <Input placeholder=\"请输入您的邮箱\" />\n                    </Form.Item>\n                  </Col>\n                </Row>\n                \n                <Form.Item\n                  name=\"category\"\n                  label=\"问题类型\"\n                  rules={[{ required: true, message: '请选择问题类型' }]}\n                >\n                  <Select placeholder=\"请选择问题类型\">\n                    <Option value=\"technical\">技术问题</Option>\n                    <Option value=\"billing\">账单问题</Option>\n                    <Option value=\"account\">账户问题</Option>\n                    <Option value=\"feature\">功能建议</Option>\n                    <Option value=\"other\">其他问题</Option>\n                  </Select>\n                </Form.Item>\n                \n                <Form.Item\n                  name=\"subject\"\n                  label=\"问题标题\"\n                  rules={[{ required: true, message: '请输入问题标题' }]}\n                >\n                  <Input placeholder=\"简要描述您的问题\" />\n                </Form.Item>\n                \n                <Form.Item\n                  name=\"description\"\n                  label=\"详细描述\"\n                  rules={[{ required: true, message: '请详细描述您的问题' }]}\n                >\n                  <TextArea \n                    rows={6} \n                    placeholder=\"请详细描述您遇到的问题，包括错误信息、操作步骤等\"\n                  />\n                </Form.Item>\n                \n                <Form.Item>\n                  <Button type=\"primary\" htmlType=\"submit\" size=\"large\" block>\n                    提交工单\n                  </Button>\n                </Form.Item>\n              </Form>\n            </Card>\n          </div>\n        </section>\n\n        {/* 常见问题 */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <Title level={2} className=\"!text-3xl !font-bold !text-gray-900\">\n                常见问题\n              </Title>\n              <Paragraph className=\"!text-lg !text-gray-600\">\n                查看常见问题解答，快速找到解决方案\n              </Paragraph>\n            </div>\n            \n            <Collapse size=\"large\" ghost>\n              {faqData.map(item => (\n                <Panel \n                  header={\n                    <div className=\"flex items-center\">\n                      <HelpCircle size={20} className=\"text-blue-600 mr-3\" />\n                      <Text strong>{item.question}</Text>\n                    </div>\n                  } \n                  key={item.key}\n                >\n                  <Paragraph className=\"text-gray-600 ml-8\">\n                    {item.answer}\n                  </Paragraph>\n                </Panel>\n              ))}\n            </Collapse>\n          </div>\n        </section>\n\n        {/* 服务时间 */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={2} className=\"!text-3xl !font-bold !text-gray-900 !mb-8\">\n              服务时间\n            </Title>\n            \n            <Row gutter={[32, 32]}>\n              <Col xs={24} md={8}>\n                <Card className=\"text-center\">\n                  <Clock className=\"text-blue-600 mx-auto mb-4\" size={32} />\n                  <Title level={4}>在线客服</Title>\n                  <Text>7×24小时</Text>\n                </Card>\n              </Col>\n              <Col xs={24} md={8}>\n                <Card className=\"text-center\">\n                  <Mail className=\"text-green-600 mx-auto mb-4\" size={32} />\n                  <Title level={4}>邮件支持</Title>\n                  <Text>工作日 9:00-18:00</Text>\n                </Card>\n              </Col>\n              <Col xs={24} md={8}>\n                <Card className=\"text-center\">\n                  <Phone className=\"text-purple-600 mx-auto mb-4\" size={32} />\n                  <Title level={4}>电话支持</Title>\n                  <Text>工作日 9:00-18:00</Text>\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        </section>\n      </Content>\n\n      <AppFooter />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AA9BA;;;;;AAgCA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAC1C,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,GAAG,yLAAA,CAAA,WAAQ;AAEX,SAAS;;IACtB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,WAAW,CAAC;QAChB,QAAQ,GAAG,CAAC,2BAA2B;QACvC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAChB,KAAK,WAAW;IAClB;IAEA,MAAM,UAAU;QACd;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA;YACE,KAAK;YACL,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,6LAAC,kIAAA,CAAA,UAAS;gBAAC,aAAY;;;;;;0BAEvB,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAA4C;;;;;;8CAGvE,6LAAC;oCAAU,WAAU;8CAA8C;;;;;;;;;;;;;;;;;kCAQvE,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAsC;;;;;;sDAGjE,6LAAC;4CAAU,WAAU;sDAA0B;;;;;;;;;;;;8CAKjD,6LAAC,+KAAA,CAAA,MAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;;sDACnB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAE9C,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;wDAAU,WAAU;kEAAqB;;;;;;kEAG1C,6LAAC,qMAAA,CAAA,SAAM;wDAAC,MAAK;wDAAU,MAAK;wDAAQ,KAAK;kEAAC;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;sDAKzB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAErC,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;wDAAU,WAAU;kEAAqB;;;;;;kEAG1C,6LAAC,qMAAA,CAAA,SAAM;wDAAC,MAAK;wDAAQ,KAAK;kEAAC;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;4DAAC,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;sDAKxB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAEtC,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;wDAAU,WAAU;kEAAqB;;;;;;kEAG1C,6LAAC,qMAAA,CAAA,SAAM;wDAAC,MAAK;wDAAQ,KAAK;kEAAC;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAsC;;;;;;sDAGjE,6LAAC;4CAAU,WAAU;sDAA0B;;;;;;;;;;;;8CAKjD,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,iLAAA,CAAA,OAAI;wCACH,MAAM;wCACN,QAAO;wCACP,UAAU;wCACV,MAAK;;0DAEL,6LAAC,+KAAA,CAAA,MAAG;gDAAC,QAAQ;;kEACX,6LAAC,+KAAA,CAAA,MAAG;wDAAC,IAAI;wDAAI,IAAI;kEACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4DACR,MAAK;4DACL,OAAM;4DACN,OAAO;gEAAC;oEAAE,UAAU;oEAAM,SAAS;gEAAU;6DAAE;sEAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gEAAC,aAAY;;;;;;;;;;;;;;;;kEAGvB,6LAAC,+KAAA,CAAA,MAAG;wDAAC,IAAI;wDAAI,IAAI;kEACf,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4DACR,MAAK;4DACL,OAAM;4DACN,OAAO;gEACL;oEAAE,UAAU;oEAAM,SAAS;gEAAQ;gEACnC;oEAAE,MAAM;oEAAS,SAAS;gEAAa;6DACxC;sEAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;gEAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;0DAKzB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;oDAAC,aAAY;;sEAClB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;0DAI1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,aAAY;;;;;;;;;;;0DAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAY;iDAAE;0DAEjD,cAAA,6LAAC;oDACC,MAAM;oDACN,aAAY;;;;;;;;;;;0DAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DACR,cAAA,6LAAC,qMAAA,CAAA,SAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,MAAK;oDAAQ,KAAK;8DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtE,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAsC;;;;;;sDAGjE,6LAAC;4CAAU,WAAU;sDAA0B;;;;;;;;;;;;8CAKjD,6LAAC,yLAAA,CAAA,WAAQ;oCAAC,MAAK;oCAAQ,KAAK;8CACzB,QAAQ,GAAG,CAAC,CAAA,qBACX,6LAAC;4CACC,sBACE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iOAAA,CAAA,aAAU;wDAAC,MAAM;wDAAI,WAAU;;;;;;kEAChC,6LAAC;wDAAK,MAAM;kEAAE,KAAK,QAAQ;;;;;;;;;;;;sDAK/B,cAAA,6LAAC;gDAAU,WAAU;0DAClB,KAAK,MAAM;;;;;;2CAHT,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;kCAYvB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAA4C;;;;;;8CAIvE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;;sDACnB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAA6B,MAAM;;;;;;kEACpD,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;wDAA8B,MAAM;;;;;;kEACpD,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC,+KAAA,CAAA,MAAG;4CAAC,IAAI;4CAAI,IAAI;sDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAA+B,MAAM;;;;;;kEACtD,6LAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;GA7RwB;;QACP,iLAAA,CAAA,OAAI,CAAC;;;KADE", "debugId": null}}]}