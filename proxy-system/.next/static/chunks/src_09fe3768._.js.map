{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Button } from 'antd';\nimport { UserOutlined, DashboardOutlined } from '@ant-design/icons';\nimport { Globe } from 'lucide-react';\nimport Link from 'next/link';\nimport { useAuth } from '@/components/AuthProvider';\n\nconst { Header } = Layout;\nconst { Title } = Typography;\n\ninterface AppHeaderProps {\n  currentPage?: 'home' | 'products' | 'pricing' | 'support';\n}\n\nexport default function AppHeader({ currentPage = 'home' }: AppHeaderProps) {\n  const { user } = useAuth();\n\n  const getLinkClass = (page: string) => {\n    return currentPage === page \n      ? \"text-blue-600 font-medium\" \n      : \"text-gray-600 hover:text-gray-900 transition-colors\";\n  };\n\n  return (\n    <Header className=\"bg-white shadow-sm px-0\" style={{ backgroundColor: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16 items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n              <Globe className=\"text-white\" size={18} />\n            </div>\n            <Title level={3} className=\"!mb-0\" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>\n          </Link>\n          \n          <div className=\"flex items-center space-x-6\">\n            <Link href=\"/\" className={getLinkClass('home')}>\n              首页\n            </Link>\n            <Link href=\"/products\" className={getLinkClass('products')}>\n              产品服务\n            </Link>\n            <Link href=\"/pricing\" className={getLinkClass('pricing')}>\n              价格方案\n            </Link>\n            <Link href=\"/support\" className={getLinkClass('support')}>\n              技术支持\n            </Link>\n            \n            <div className=\"flex items-center space-x-4 ml-4\">\n              {!user ? (\n                <>\n                  <Link href=\"/auth/login\">\n                    <Button type=\"text\" icon={<UserOutlined />} style={{ color: '#374151' }}>\n                      登录\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth/register\">\n                    <Button \n                      type=\"primary\" \n                      className=\"bg-gradient-to-r from-blue-500 to-indigo-600 border-0\"\n                      style={{ background: 'linear-gradient(to right, #3b82f6, #4f46e5)', border: 'none' }}\n                    >\n                      免费注册\n                    </Button>\n                  </Link>\n                </>\n              ) : (\n                <Link href=\"/dashboard\">\n                  <Button type=\"primary\" icon={<DashboardOutlined />}>\n                    控制台\n                  </Button>\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </Header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMb,SAAS,UAAU,EAAE,cAAc,MAAM,EAAkB;;IACxE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,OAAO,gBAAgB,OACnB,8BACA;IACN;IAEA,qBACE,6LAAC;QAAO,WAAU;QAA0B,OAAO;YAAE,iBAAiB;QAAQ;kBAC5E,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,MAAM;;;;;;;;;;;0CAEtC,6LAAC;gCAAM,OAAO;gCAAG,WAAU;gCAAQ,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAE;0CAAG;;;;;;;;;;;;kCAG7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAW,aAAa;0CAAS;;;;;;0CAGhD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAW,aAAa;0CAAa;;;;;;0CAG5D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAG1D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;0CACZ,CAAC,qBACA;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDAAK,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;sDAI3E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,YAAY;oDAA+C,QAAQ;gDAAO;0DACpF;;;;;;;;;;;;iEAML,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtE;GAjEwB;;QACL,qIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppFooter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Row, Col, Divider } from 'antd';\nimport { Globe, Users, Clock } from 'lucide-react';\nimport Link from 'next/link';\n\nconst { Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\n\nexport default function AppFooter() {\n  return (\n    <Footer className=\"bg-gray-900 text-white\" style={{ backgroundColor: '#111827', color: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <Row gutter={[48, 32]}>\n          <Col xs={24} sm={12} lg={6}>\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <Globe className=\"text-white\" size={18} />\n              </div>\n              <Title level={4} className=\"!mb-0\" style={{ color: 'white', margin: 0 }}>ProxyHub</Title>\n            </div>\n            <Paragraph className=\"!mb-6\" style={{ color: '#9ca3af', marginBottom: '1.5rem' }}>\n              专业的代理服务平台，为您的业务提供稳定可靠的网络代理解决方案。\n            </Paragraph>\n            <div className=\"flex space-x-4\">\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Globe className=\"text-gray-400\" size={20} />\n              </div>\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Users className=\"text-gray-400\" size={20} />\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>产品服务</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv4 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv6 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  共享代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/pricing\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  企业定制\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>支持中心</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  使用文档\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  API 接口\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  常见问题\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  技术支持\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>联系我们</Title>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"text-gray-400\" size={16} />\n                <Text style={{ color: '#9ca3af' }}>7×24小时服务</Text>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  在线客服\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  工单系统\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  商务合作\n                </Link>\n              </div>\n            </div>\n          </Col>\n        </Row>\n        \n        <Divider style={{ borderColor: '#374151', margin: '2rem 0' }} />\n        \n        <div className=\"flex flex-col md:flex-row justify-between items-center\">\n          <Text style={{ color: '#9ca3af' }}>\n            © 2024 ProxyHub. 保留所有权利.\n          </Text>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              隐私政策\n            </Link>\n            <Link href=\"/terms\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              服务条款\n            </Link>\n            <Link href=\"/legal\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              法律声明\n            </Link>\n          </div>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;QAAyB,OAAO;YAAE,iBAAiB;YAAW,OAAO;QAAQ;kBAC7F,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCACnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAA<PERSON>,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;sDAEtC,6LAAC;4CAAM,OAAO;4CAAG,WAAU;4CAAQ,OAAO;gDAAE,OAAO;gDAAS,QAAQ;4CAAE;sDAAG;;;;;;;;;;;;8CAE3E,6LAAC;oCAAU,WAAU;oCAAQ,OAAO;wCAAE,OAAO;wCAAW,cAAc;oCAAS;8CAAG;;;;;;8CAGlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAK7C,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAgB,MAAM;;;;;;8DACvC,6LAAC;oDAAK,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAErC,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1G,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,aAAa;wBAAW,QAAQ;oBAAS;;;;;;8BAE3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAGnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGlG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGhG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5G;KA5HwB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Layout, \n  Typography, \n  Card, \n  Row, \n  Col, \n  Button, \n  Switch,\n  Space,\n  Tag,\n  Divider\n} from 'antd';\nimport { \n  Globe,\n  CheckCircle,\n  Star,\n  Zap,\n  Shield\n} from 'lucide-react';\nimport Link from 'next/link';\nimport AppHeader from '@/components/AppHeader';\nimport AppFooter from '@/components/AppFooter';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\n\nexport default function PricingPage() {\n  const [isYearly, setIsYearly] = useState(false);\n\n  const plans = [\n    {\n      name: '基础版',\n      description: '适合个人用户和小型项目',\n      icon: Globe,\n      color: 'from-blue-500 to-indigo-600',\n      monthlyPrice: 99,\n      yearlyPrice: 999,\n      features: [\n        '10个IPv4代理',\n        '基础技术支持',\n        '99% 可用性保证',\n        'HTTP/SOCKS5 协议',\n        '标准速度'\n      ],\n      popular: false\n    },\n    {\n      name: '专业版',\n      description: '适合中小企业和专业用户',\n      icon: Zap,\n      color: 'from-purple-500 to-pink-600',\n      monthlyPrice: 299,\n      yearlyPrice: 2999,\n      features: [\n        '50个IPv4代理',\n        '优先技术支持',\n        '99.9% 可用性保证',\n        'HTTP/SOCKS5 协议',\n        '高速连接',\n        'IPv6 代理支持',\n        '自定义配置'\n      ],\n      popular: true\n    },\n    {\n      name: '企业版',\n      description: '适合大型企业和高需求用户',\n      icon: Shield,\n      color: 'from-green-500 to-emerald-600',\n      monthlyPrice: 999,\n      yearlyPrice: 9999,\n      features: [\n        '200个IPv4代理',\n        '24/7 专属支持',\n        '99.99% 可用性保证',\n        'HTTP/SOCKS5 协议',\n        '极速连接',\n        'IPv6 代理支持',\n        '完全自定义',\n        'API 接口',\n        '专属客户经理'\n      ],\n      popular: false\n    }\n  ];\n\n  return (\n    <Layout className=\"min-h-screen\">\n      <AppHeader currentPage=\"pricing\" />\n\n      <Content>\n        {/* 页面标题 */}\n        <section className=\"bg-gradient-to-r from-blue-50 to-indigo-50 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={1} className=\"!text-4xl !font-bold !text-gray-900 !mb-4\">\n              价格方案\n            </Title>\n            <Paragraph className=\"!text-xl !text-gray-600 !max-w-3xl !mx-auto !mb-8\">\n              选择最适合您需求的代理服务方案，所有方案都包含核心功能，\n              无隐藏费用，随时可以升级或降级。\n            </Paragraph>\n            \n            {/* 计费周期切换 */}\n            <div className=\"flex items-center justify-center space-x-4\">\n              <Text className={!isYearly ? 'font-medium text-blue-600' : 'text-gray-600'}>月付</Text>\n              <Switch \n                checked={isYearly} \n                onChange={setIsYearly}\n                size=\"large\"\n              />\n              <Text className={isYearly ? 'font-medium text-blue-600' : 'text-gray-600'}>年付</Text>\n              <Tag color=\"green\">年付享8.5折</Tag>\n            </div>\n          </div>\n        </section>\n\n        {/* 价格卡片 */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <Row gutter={[32, 32]} justify=\"center\">\n              {plans.map((plan, index) => {\n                const PlanIcon = plan.icon;\n                const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice;\n                const originalYearlyPrice = plan.monthlyPrice * 12;\n                const savings = originalYearlyPrice - plan.yearlyPrice;\n                \n                return (\n                  <Col xs={24} lg={8} key={index}>\n                    <Card \n                      className={`h-full text-center relative ${\n                        plan.popular ? 'border-2 border-blue-500 shadow-2xl' : 'shadow-lg'\n                      }`}\n                      bodyStyle={{ padding: '2rem' }}\n                    >\n                      {plan.popular && (\n                        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                          <Tag color=\"blue\" className=\"px-4 py-1 text-sm font-medium\">\n                            <Star size={14} className=\"inline mr-1\" />\n                            最受欢迎\n                          </Tag>\n                        </div>\n                      )}\n                      \n                      <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mx-auto mb-6`}>\n                        <PlanIcon className=\"text-white\" size={32} />\n                      </div>\n                      \n                      <Title level={3} className=\"!mb-2\">{plan.name}</Title>\n                      <Paragraph className=\"text-gray-600 !mb-6\">{plan.description}</Paragraph>\n                      \n                      <div className=\"mb-6\">\n                        <div className=\"flex items-baseline justify-center\">\n                          <span className=\"text-4xl font-bold text-gray-900\">¥{price}</span>\n                          <span className=\"text-gray-600 ml-2\">/{isYearly ? '年' : '月'}</span>\n                        </div>\n                        {isYearly && (\n                          <div className=\"mt-2\">\n                            <Text className=\"text-sm text-gray-500 line-through\">¥{originalYearlyPrice}/年</Text>\n                            <Text className=\"text-sm text-green-600 ml-2\">节省 ¥{savings}</Text>\n                          </div>\n                        )}\n                      </div>\n                      \n                      <div className=\"space-y-4 mb-8\">\n                        {plan.features.map((feature, featureIndex) => (\n                          <div key={featureIndex} className=\"flex items-center\">\n                            <CheckCircle size={16} className=\"text-green-500 mr-3 flex-shrink-0\" />\n                            <Text>{feature}</Text>\n                          </div>\n                        ))}\n                      </div>\n                      \n                      <Link href=\"/auth/register\">\n                        <Button \n                          type={plan.popular ? \"primary\" : \"default\"}\n                          size=\"large\"\n                          block\n                          className={plan.popular ? \"h-12 bg-gradient-to-r from-blue-500 to-indigo-600 border-0\" : \"h-12\"}\n                        >\n                          {plan.popular ? \"立即开始\" : \"选择方案\"}\n                        </Button>\n                      </Link>\n                    </Card>\n                  </Col>\n                );\n              })}\n            </Row>\n          </div>\n        </section>\n\n        {/* 常见问题 */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <Title level={2} className=\"!text-3xl !font-bold !text-gray-900\">\n                常见问题\n              </Title>\n            </div>\n            \n            <div className=\"space-y-8\">\n              <Card>\n                <Title level={4}>可以随时升级或降级方案吗？</Title>\n                <Paragraph>\n                  是的，您可以随时升级或降级您的方案。升级会立即生效，降级会在下个计费周期生效。\n                  我们会按比例计算费用差额。\n                </Paragraph>\n              </Card>\n              \n              <Card>\n                <Title level={4}>支持哪些支付方式？</Title>\n                <Paragraph>\n                  我们支持支付宝、微信支付、银行卡等多种支付方式。企业用户还可以申请月结账单。\n                </Paragraph>\n              </Card>\n              \n              <Card>\n                <Title level={4}>有免费试用吗？</Title>\n                <Paragraph>\n                  新用户注册即可获得免费试用额度，可以体验我们的服务质量。\n                  试用期间享受完整功能，无任何限制。\n                </Paragraph>\n              </Card>\n              \n              <Card>\n                <Title level={4}>如何获得技术支持？</Title>\n                <Paragraph>\n                  我们提供多种技术支持方式：在线客服、工单系统、邮件支持。\n                  专业版和企业版用户享受优先支持服务。\n                </Paragraph>\n              </Card>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA区域 */}\n        <section className=\"py-16 bg-gradient-to-r from-blue-600 to-indigo-700\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={2} className=\"!text-3xl !font-bold !text-white !mb-4\">\n              还有疑问？\n            </Title>\n            <Paragraph className=\"!text-xl !text-blue-100 !mb-8\">\n              联系我们的销售团队，获得个性化的方案建议\n            </Paragraph>\n            <Space size=\"large\">\n              <Link href=\"/support\">\n                <Button \n                  type=\"primary\" \n                  size=\"large\" \n                  className=\"h-12 px-8 text-lg bg-white text-blue-600 border-0 hover:bg-gray-100\"\n                >\n                  联系销售\n                </Button>\n              </Link>\n              <Link href=\"/auth/register\">\n                <Button \n                  size=\"large\" \n                  className=\"h-12 px-8 text-lg text-white border-white hover:bg-white hover:text-blue-600\"\n                >\n                  免费试用\n                </Button>\n              </Link>\n            </Space>\n          </div>\n        </section>\n      </Content>\n\n      <AppFooter />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;;;AAxBA;;;;;;;AA0BA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAC1C,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,aAAa;YACb,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,cAAc;YACd,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,cAAc;YACd,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,cAAc;YACd,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;KACD;IAED,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,6LAAC,kIAAA,CAAA,UAAS;gBAAC,aAAY;;;;;;0BAEvB,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAA4C;;;;;;8CAGvE,6LAAC;oCAAU,WAAU;8CAAoD;;;;;;8CAMzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,WAAW,8BAA8B;sDAAiB;;;;;;sDAC5E,6LAAC,qLAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,MAAK;;;;;;sDAEP,6LAAC;4CAAK,WAAW,WAAW,8BAA8B;sDAAiB;;;;;;sDAC3E,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;gCAAE,SAAQ;0CAC5B,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,MAAM,WAAW,KAAK,IAAI;oCAC1B,MAAM,QAAQ,WAAW,KAAK,WAAW,GAAG,KAAK,YAAY;oCAC7D,MAAM,sBAAsB,KAAK,YAAY,GAAG;oCAChD,MAAM,UAAU,sBAAsB,KAAK,WAAW;oCAEtD,qBACE,6LAAC,+KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CACH,WAAW,CAAC,4BAA4B,EACtC,KAAK,OAAO,GAAG,wCAAwC,aACvD;4CACF,WAAW;gDAAE,SAAS;4CAAO;;gDAE5B,KAAK,OAAO,kBACX,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;wDAAC,OAAM;wDAAO,WAAU;;0EAC1B,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAgB;;;;;;;;;;;;8DAMhD,6LAAC;oDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,2DAA2D,CAAC;8DACnH,cAAA,6LAAC;wDAAS,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAGzC,6LAAC;oDAAM,OAAO;oDAAG,WAAU;8DAAS,KAAK,IAAI;;;;;;8DAC7C,6LAAC;oDAAU,WAAU;8DAAuB,KAAK,WAAW;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAmC;wEAAE;;;;;;;8EACrD,6LAAC;oEAAK,WAAU;;wEAAqB;wEAAE,WAAW,MAAM;;;;;;;;;;;;;wDAEzD,0BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAqC;wEAAE;wEAAoB;;;;;;;8EAC3E,6LAAC;oEAAK,WAAU;;wEAA8B;wEAAK;;;;;;;;;;;;;;;;;;;8DAKzD,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;4DAAuB,WAAU;;8EAChC,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EACjC,6LAAC;8EAAM;;;;;;;2DAFC;;;;;;;;;;8DAOd,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wDACL,MAAM,KAAK,OAAO,GAAG,YAAY;wDACjC,MAAK;wDACL,KAAK;wDACL,WAAW,KAAK,OAAO,GAAG,+DAA+D;kEAExF,KAAK,OAAO,GAAG,SAAS;;;;;;;;;;;;;;;;;uCApDR;;;;;gCA0D7B;;;;;;;;;;;;;;;;kCAMN,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAKnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iLAAA,CAAA,OAAI;;8DACH,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;8DAAU;;;;;;;;;;;;sDAMb,6LAAC,iLAAA,CAAA,OAAI;;8DACH,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;8DAAU;;;;;;;;;;;;sDAKb,6LAAC,iLAAA,CAAA,OAAI;;8DACH,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;8DAAU;;;;;;;;;;;;sDAMb,6LAAC,iLAAA,CAAA,OAAI;;8DACH,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAyC;;;;;;8CAGpE,6LAAC;oCAAU,WAAU;8CAAgC;;;;;;8CAGrD,6LAAC,mMAAA,CAAA,QAAK;oCAAC,MAAK;;sDACV,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;GAnPwB;KAAA", "debugId": null}}]}