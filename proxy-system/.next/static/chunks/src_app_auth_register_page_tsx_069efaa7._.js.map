{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Typography, message, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/components/AuthProvider';\nimport LoadingScreen from '@/components/LoadingScreen';\n\nconst { Title, Text } = Typography;\n\nexport default function RegisterPage() {\n  const [loading, setLoading] = useState(false);\n  const { register } = useAuth();\n  const router = useRouter();\n\n  const onFinish = async (values: { email: string; username: string; password: string; confirmPassword: string }) => {\n    setLoading(true);\n    try {\n      await register(values.email, values.username, values.password);\n      message.success('注册成功');\n      router.push('/dashboard');\n    } catch (error) {\n      message.error(error instanceof Error ? error.message : '注册失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <Title level={2}>代理系统</Title>\n          <Text type=\"secondary\">创建您的账户</Text>\n        </div>\n        \n        <Card>\n          <Form\n            name=\"register\"\n            onFinish={onFinish}\n            autoComplete=\"off\"\n            layout=\"vertical\"\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"email\"\n              label=\"邮箱\"\n              rules={[\n                { required: true, message: '请输入邮箱' },\n                { type: 'email', message: '请输入有效的邮箱地址' }\n              ]}\n            >\n              <Input\n                prefix={<MailOutlined />}\n                placeholder=\"请输入邮箱\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"username\"\n              label=\"用户名\"\n              rules={[\n                { required: true, message: '请输入用户名' },\n                { min: 2, message: '用户名至少2个字符' },\n                { max: 20, message: '用户名最多20个字符' }\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 8, message: '密码至少8个字符' },\n                {\n                  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/,\n                  message: '密码必须包含大小写字母和数字'\n                }\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请输入密码\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"confirmPassword\"\n              label=\"确认密码\"\n              dependencies={['password']}\n              rules={[\n                { required: true, message: '请确认密码' },\n                ({ getFieldValue }) => ({\n                  validator(_, value) {\n                    if (!value || getFieldValue('password') === value) {\n                      return Promise.resolve();\n                    }\n                    return Promise.reject(new Error('两次输入的密码不一致'));\n                  },\n                }),\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请再次输入密码\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                block\n              >\n                注册\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <Divider>或</Divider>\n\n          <div className=\"text-center\">\n            <Text>\n              已有账户？{' '}\n              <Link href=\"/auth/login\" className=\"text-blue-600 hover:text-blue-500\">\n                立即登录\n              </Link>\n            </Text>\n          </div>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAEnB,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,OAAO,QAAQ;YAC7D,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,6LAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;8BAGzB,6LAAC,iLAAA,CAAA,OAAI;;sCACH,6LAAC,iLAAA,CAAA,OAAI;4BACH,MAAK;4BACL,UAAU;4BACV,cAAa;4BACb,QAAO;4BACP,MAAK;;8CAEL,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAQ;wCACnC;4CAAE,MAAM;4CAAS,SAAS;wCAAa;qCACxC;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAS;wCACpC;4CAAE,KAAK;4CAAG,SAAS;wCAAY;wCAC/B;4CAAE,KAAK;4CAAI,SAAS;wCAAa;qCAClC;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAQ;wCACnC;4CAAE,KAAK;4CAAG,SAAS;wCAAW;wCAC9B;4CACE,SAAS;4CACT,SAAS;wCACX;qCACD;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;wCAAC;qCAAW;oCAC1B,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAQ;wCACnC,CAAC,EAAE,aAAa,EAAE,GAAK,CAAC;gDACtB,WAAU,CAAC,EAAE,KAAK;oDAChB,IAAI,CAAC,SAAS,cAAc,gBAAgB,OAAO;wDACjD,OAAO,QAAQ,OAAO;oDACxB;oDACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;gDAClC;4CACF,CAAC;qCACF;8CAED,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;wCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACrB,aAAY;;;;;;;;;;;8CAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;8CACR,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,KAAK;kDACN;;;;;;;;;;;;;;;;;sCAML,6LAAC,uLAAA,CAAA,UAAO;sCAAC;;;;;;sCAET,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;oCAAK;oCACE;kDACN,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF;GAjIwB;;QAED,qIAAA,CAAA,UAAO;QACb,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}