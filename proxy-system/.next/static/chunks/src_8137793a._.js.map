{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Button } from 'antd';\nimport { UserOutlined, DashboardOutlined } from '@ant-design/icons';\nimport { Globe } from 'lucide-react';\nimport Link from 'next/link';\nimport { useAuth } from '@/components/AuthProvider';\n\nconst { Header } = Layout;\nconst { Title } = Typography;\n\ninterface AppHeaderProps {\n  currentPage?: 'home' | 'products' | 'pricing' | 'support';\n}\n\nexport default function AppHeader({ currentPage = 'home' }: AppHeaderProps) {\n  const { user } = useAuth();\n\n  const getLinkClass = (page: string) => {\n    return currentPage === page \n      ? \"text-blue-600 font-medium\" \n      : \"text-gray-600 hover:text-gray-900 transition-colors\";\n  };\n\n  return (\n    <Header className=\"bg-white shadow-sm px-0\" style={{ backgroundColor: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16 items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n              <Globe className=\"text-white\" size={18} />\n            </div>\n            <Title level={3} className=\"!mb-0\" style={{ color: '#1f2937', margin: 0 }}>ProxyHub</Title>\n          </Link>\n          \n          <div className=\"flex items-center space-x-6\">\n            <Link href=\"/\" className={getLinkClass('home')}>\n              首页\n            </Link>\n            <Link href=\"/products\" className={getLinkClass('products')}>\n              产品服务\n            </Link>\n            <Link href=\"/pricing\" className={getLinkClass('pricing')}>\n              价格方案\n            </Link>\n            <Link href=\"/support\" className={getLinkClass('support')}>\n              技术支持\n            </Link>\n            \n            <div className=\"flex items-center space-x-4 ml-4\">\n              {!user ? (\n                <>\n                  <Link href=\"/auth/login\">\n                    <Button type=\"text\" icon={<UserOutlined />} style={{ color: '#374151' }}>\n                      登录\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth/register\">\n                    <Button \n                      type=\"primary\" \n                      className=\"bg-gradient-to-r from-blue-500 to-indigo-600 border-0\"\n                      style={{ background: 'linear-gradient(to right, #3b82f6, #4f46e5)', border: 'none' }}\n                    >\n                      免费注册\n                    </Button>\n                  </Link>\n                </>\n              ) : (\n                <Link href=\"/dashboard\">\n                  <Button type=\"primary\" icon={<DashboardOutlined />}>\n                    控制台\n                  </Button>\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </Header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAMb,SAAS,UAAU,EAAE,cAAc,MAAM,EAAkB;;IACxE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,OAAO,gBAAgB,OACnB,8BACA;IACN;IAEA,qBACE,6LAAC;QAAO,WAAU;QAA0B,OAAO;YAAE,iBAAiB;QAAQ;kBAC5E,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,MAAM;;;;;;;;;;;0CAEtC,6LAAC;gCAAM,OAAO;gCAAG,WAAU;gCAAQ,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAE;0CAAG;;;;;;;;;;;;kCAG7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAW,aAAa;0CAAS;;;;;;0CAGhD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAW,aAAa;0CAAa;;;;;;0CAG5D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAG1D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAW,aAAa;0CAAY;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;0CACZ,CAAC,qBACA;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDAAK,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;sDAI3E,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,OAAO;oDAAE,YAAY;oDAA+C,QAAQ;gDAAO;0DACpF;;;;;;;;;;;;iEAML,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtE;GAjEwB;;QACL,qIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/components/AppFooter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Typography, Row, Col, Divider } from 'antd';\nimport { Globe, Users, Clock } from 'lucide-react';\nimport Link from 'next/link';\n\nconst { Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\n\nexport default function AppFooter() {\n  return (\n    <Footer className=\"bg-gray-900 text-white\" style={{ backgroundColor: '#111827', color: 'white' }}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <Row gutter={[48, 32]}>\n          <Col xs={24} sm={12} lg={6}>\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <Globe className=\"text-white\" size={18} />\n              </div>\n              <Title level={4} className=\"!mb-0\" style={{ color: 'white', margin: 0 }}>ProxyHub</Title>\n            </div>\n            <Paragraph className=\"!mb-6\" style={{ color: '#9ca3af', marginBottom: '1.5rem' }}>\n              专业的代理服务平台，为您的业务提供稳定可靠的网络代理解决方案。\n            </Paragraph>\n            <div className=\"flex space-x-4\">\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Globe className=\"text-gray-400\" size={20} />\n              </div>\n              <div className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors\">\n                <Users className=\"text-gray-400\" size={20} />\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>产品服务</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv4 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  IPv6 代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/products\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  共享代理\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/pricing\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  企业定制\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>支持中心</Title>\n            <div className=\"space-y-3\">\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  使用文档\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  API 接口\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  常见问题\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  技术支持\n                </Link>\n              </div>\n            </div>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Title level={5} style={{ color: 'white', marginBottom: '1rem' }}>联系我们</Title>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"text-gray-400\" size={16} />\n                <Text style={{ color: '#9ca3af' }}>7×24小时服务</Text>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  在线客服\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  工单系统\n                </Link>\n              </div>\n              <div>\n                <Link href=\"/support\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n                  商务合作\n                </Link>\n              </div>\n            </div>\n          </Col>\n        </Row>\n        \n        <Divider style={{ borderColor: '#374151', margin: '2rem 0' }} />\n        \n        <div className=\"flex flex-col md:flex-row justify-between items-center\">\n          <Text style={{ color: '#9ca3af' }}>\n            © 2024 ProxyHub. 保留所有权利.\n          </Text>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/privacy\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              隐私政策\n            </Link>\n            <Link href=\"/terms\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              服务条款\n            </Link>\n            <Link href=\"/legal\" style={{ color: '#9ca3af' }} className=\"hover:text-white transition-colors\">\n              法律声明\n            </Link>\n          </div>\n        </div>\n      </div>\n    </Footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;QAAyB,OAAO;YAAE,iBAAiB;YAAW,OAAO;QAAQ;kBAC7F,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCACnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAA<PERSON>,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;sDAEtC,6LAAC;4CAAM,OAAO;4CAAG,WAAU;4CAAQ,OAAO;gDAAE,OAAO;gDAAS,QAAQ;4CAAE;sDAAG;;;;;;;;;;;;8CAE3E,6LAAC;oCAAU,WAAU;oCAAQ,OAAO;wCAAE,OAAO;wCAAW,cAAc;oCAAS;8CAAG;;;;;;8CAGlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAK7C,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIrG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAOxG,6LAAC,+KAAA,CAAA,MAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;;8CACvB,6LAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,OAAO;wCAAS,cAAc;oCAAO;8CAAG;;;;;;8CAClE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAgB,MAAM;;;;;;8DACvC,6LAAC;oDAAK,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAErC,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,OAAO;oDAAE,OAAO;gDAAU;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1G,6LAAC,uLAAA,CAAA,UAAO;oBAAC,OAAO;wBAAE,aAAa;wBAAW,QAAQ;oBAAS;;;;;;8BAE3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;sCAGnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGlG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;8CAGhG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAU;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5G;KA5HwB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///www/wwwroot/proxy/proxy_nextjs/proxy-system/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { \n  Layout, \n  Typography, \n  Card, \n  Row, \n  Col, \n  Button, \n  Table, \n  Tag,\n  Space,\n  Divider\n} from 'antd';\nimport { \n  Globe,\n  Wifi,\n  Users,\n  Award,\n  CheckCircle,\n  ArrowRight,\n  Star\n} from 'lucide-react';\nimport Link from 'next/link';\nimport AppHeader from '@/components/AppHeader';\nimport AppFooter from '@/components/AppFooter';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title, Paragraph, Text } = Typography;\n\nexport default function ProductsPage() {\n  const pricingData = [\n    {\n      key: '1',\n      type: 'IPv4 独享',\n      description: '独享IP地址，高速稳定',\n      price: '¥15/天',\n      features: ['独享带宽', '99.9%可用性', 'HTTP/SOCKS5', '24/7支持'],\n      popular: true\n    },\n    {\n      key: '2',\n      type: 'IPv6 代理',\n      description: '下一代协议，价格优惠',\n      price: '¥8/天',\n      features: ['海量IP池', '高性价比', '未来标准', '批量优惠'],\n      popular: false\n    },\n    {\n      key: '3',\n      type: '共享代理',\n      description: '经济实惠，功能完整',\n      price: '¥5/天',\n      features: ['基础功能', '入门首选', '快速部署', '成本低廉'],\n      popular: false\n    }\n  ];\n\n  const columns = [\n    {\n      title: '代理类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (text: string, record: any) => (\n        <div className=\"flex items-center space-x-2\">\n          <div className={`w-3 h-3 rounded-full ${record.popular ? 'bg-orange-500' : 'bg-blue-500'}`}></div>\n          <span className=\"font-medium\">{text}</span>\n          {record.popular && <Tag color=\"orange\">推荐</Tag>}\n        </div>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n    },\n    {\n      title: '价格',\n      dataIndex: 'price',\n      key: 'price',\n      render: (text: string) => <Text strong className=\"text-lg text-blue-600\">{text}</Text>,\n    },\n    {\n      title: '特性',\n      dataIndex: 'features',\n      key: 'features',\n      render: (features: string[]) => (\n        <div className=\"space-y-1\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"flex items-center space-x-1\">\n              <CheckCircle size={14} className=\"text-green-500\" />\n              <Text className=\"text-sm\">{feature}</Text>\n            </div>\n          ))}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: () => (\n        <Link href=\"/auth/register\">\n          <Button type=\"primary\" icon={<ArrowRight size={16} />}>\n            立即购买\n          </Button>\n        </Link>\n      ),\n    },\n  ];\n\n  return (\n    <Layout className=\"min-h-screen\">\n      <AppHeader currentPage=\"products\" />\n\n      <Content>\n        {/* 页面标题 */}\n        <section className=\"bg-gradient-to-r from-blue-50 to-indigo-50 py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={1} className=\"!text-4xl !font-bold !text-gray-900 !mb-4\">\n              产品服务\n            </Title>\n            <Paragraph className=\"!text-xl !text-gray-600 !max-w-3xl !mx-auto\">\n              提供多种类型的代理服务，满足不同业务需求。从个人使用到企业级应用，\n              我们都有相应的解决方案。\n            </Paragraph>\n          </div>\n        </section>\n\n        {/* 产品特性 */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <Row gutter={[48, 48]}>\n              <Col xs={24} lg={8}>\n                <Card className=\"text-center h-full border-0 shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <Award className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>IPv4 独享代理</Title>\n                  <Paragraph className=\"text-gray-600\">\n                    独享IP地址，提供最佳性能和稳定性。适合对速度和稳定性要求较高的业务场景。\n                  </Paragraph>\n                  <ul className=\"text-left space-y-2 mt-4\">\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />独享带宽，无共享风险</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />99.9% 服务可用性</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />支持HTTP/HTTPS和SOCKS5</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />24/7 技术支持</li>\n                  </ul>\n                </Card>\n              </Col>\n              \n              <Col xs={24} lg={8}>\n                <Card className=\"text-center h-full border-0 shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <Wifi className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>IPv6 代理</Title>\n                  <Paragraph className=\"text-gray-600\">\n                    下一代互联网协议，提供更大的地址空间和更好的性能，价格更优惠。\n                  </Paragraph>\n                  <ul className=\"text-left space-y-2 mt-4\">\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />海量IP资源池</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />价格更加优惠</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />未来网络标准</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />批量购买优惠</li>\n                  </ul>\n                </Card>\n              </Col>\n              \n              <Col xs={24} lg={8}>\n                <Card className=\"text-center h-full border-0 shadow-lg\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <Users className=\"text-white\" size={32} />\n                  </div>\n                  <Title level={4}>共享代理</Title>\n                  <Paragraph className=\"text-gray-600\">\n                    经济实惠的共享IPv4代理，适合预算有限的用户，功能完整。\n                  </Paragraph>\n                  <ul className=\"text-left space-y-2 mt-4\">\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />价格经济实惠</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />基础功能完整</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />适合入门用户</li>\n                    <li className=\"flex items-center\"><CheckCircle size={16} className=\"text-green-500 mr-2\" />快速部署使用</li>\n                  </ul>\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        </section>\n\n        {/* 价格对比表 */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <Title level={2} className=\"!text-3xl !font-bold !text-gray-900\">\n                价格对比\n              </Title>\n              <Paragraph className=\"!text-lg !text-gray-600\">\n                选择最适合您需求的代理服务方案\n              </Paragraph>\n            </div>\n            \n            <Card className=\"shadow-lg\">\n              <Table \n                columns={columns} \n                dataSource={pricingData} \n                pagination={false}\n                className=\"custom-table\"\n              />\n            </Card>\n            \n            <div className=\"text-center mt-8\">\n              <Text className=\"text-gray-500\">\n                * 价格仅供参考，实际价格可能因促销活动而有所调整\n              </Text>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA区域 */}\n        <section className=\"py-16 bg-gradient-to-r from-blue-600 to-indigo-700\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <Title level={2} className=\"!text-3xl !font-bold !text-white !mb-4\">\n              准备开始使用？\n            </Title>\n            <Paragraph className=\"!text-xl !text-blue-100 !mb-8\">\n              立即注册，享受专业的代理服务\n            </Paragraph>\n            <Space size=\"large\">\n              <Link href=\"/auth/register\">\n                <Button \n                  type=\"primary\" \n                  size=\"large\" \n                  className=\"h-12 px-8 text-lg bg-white text-blue-600 border-0 hover:bg-gray-100\"\n                >\n                  免费注册\n                </Button>\n              </Link>\n              <Link href=\"/pricing\">\n                <Button \n                  size=\"large\" \n                  className=\"h-12 px-8 text-lg text-white border-white hover:bg-white hover:text-blue-600\"\n                >\n                  查看价格\n                </Button>\n              </Link>\n            </Space>\n          </div>\n        </section>\n      </Content>\n\n      <AppFooter />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AA1BA;;;;;;;AA4BA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAC1C,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;IACtB,MAAM,cAAc;QAClB;YACE,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAQ;gBAAY;gBAAe;aAAS;YACvD,SAAS;QACX;QACA;YACE,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAO;YAC3C,SAAS;QACX;QACA;YACE,KAAK;YACL,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;YAC1C,SAAS;QACX;KACD;IAED,MAAM,UAAU;QACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAc,uBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,CAAC,qBAAqB,EAAE,OAAO,OAAO,GAAG,kBAAkB,eAAe;;;;;;sCAC1F,6LAAC;4BAAK,WAAU;sCAAe;;;;;;wBAC9B,OAAO,OAAO,kBAAI,6LAAC,+KAAA,CAAA,MAAG;4BAAC,OAAM;sCAAS;;;;;;;;;;;;QAG7C;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBAAiB,6LAAC;oBAAK,MAAM;oBAAC,WAAU;8BAAyB;;;;;;QAC5E;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,yBACP,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC,8NAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACjC,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;2BAFnB;;;;;;;;;;QAOlB;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,kBACN,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAU,oBAAM,6LAAC,qNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;kCAAQ;;;;;;;;;;;QAK7D;KACD;IAED,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,6LAAC,kIAAA,CAAA,UAAS;gBAAC,aAAY;;;;;;0BAEvB,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAA4C;;;;;;8CAGvE,6LAAC;oCAAU,WAAU;8CAA8C;;;;;;;;;;;;;;;;;kCAQvE,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;;kDACnB,6LAAC,+KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAEtC,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;oDAAU,WAAU;8DAAgB;;;;;;8DAGrC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAKjG,6LAAC,+KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAErC,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;oDAAU,WAAU;8DAAgB;;;;;;8DAGrC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAKjG,6LAAC,+KAAA,CAAA,MAAG;wCAAC,IAAI;wCAAI,IAAI;kDACf,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;8DAEtC,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;oDAAU,WAAU;8DAAgB;;;;;;8DAGrC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;sEAC3F,6LAAC;4DAAG,WAAU;;8EAAoB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvG,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAsC;;;;;;sDAGjE,6LAAC;4CAAU,WAAU;sDAA0B;;;;;;;;;;;;8CAKjD,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCACJ,SAAS;wCACT,YAAY;wCACZ,YAAY;wCACZ,WAAU;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;kCAQtC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,OAAO;oCAAG,WAAU;8CAAyC;;;;;;8CAGpE,6LAAC;oCAAU,WAAU;8CAAgC;;;;;;8CAGrD,6LAAC,mMAAA,CAAA,QAAK;oCAAC,MAAK;;sDACV,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qMAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KA/NwB", "debugId": null}}]}