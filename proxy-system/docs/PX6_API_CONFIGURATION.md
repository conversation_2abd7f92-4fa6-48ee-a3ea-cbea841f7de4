# PX6 API 密钥配置指南

## 概述

PX6_API_KEY 密钥配置已从 `.env.local` 文件迁移到后台管理页面，提供更安全和灵活的配置管理方式。

## 配置方式

### 1. 访问管理员设置页面

1. 使用管理员账户登录系统
2. 访问 `/dashboard/settings` 页面
3. 在 "PX6 API配置" 部分进行设置

### 2. 配置项说明

#### PX6 API密钥 (px6_api_key)
- **描述**: PX6服务的API访问密钥
- **必填**: 是
- **格式**: 字符串，最少10个字符
- **示例**: `px6_1234567890abcdef`

#### PX6 API地址 (px6_api_url)
- **描述**: PX6 API服务的基础URL
- **必填**: 是
- **默认值**: `https://api.px6.io`
- **格式**: 有效的URL地址

### 3. 其他系统配置

#### 系统配置
- **每用户最大代理数量**: 限制单个用户可创建的代理数量
- **默认代理时长**: 新创建代理的默认有效期（天）
- **启用自动续费**: 是否允许代理自动续费
- **维护模式**: 开启后系统进入维护状态

#### 系统通知
- **系统公告**: 在用户控制台顶部显示的公告信息

## 功能特性

### 1. 安全性
- ✅ 密钥不再存储在代码仓库中
- ✅ 支持密钥可见性切换
- ✅ 管理员权限验证
- ✅ 操作日志记录

### 2. 便利性
- ✅ 实时配置更新
- ✅ API连接测试功能
- ✅ 表单验证和错误提示
- ✅ 一键重置功能

### 3. 可靠性
- ✅ 数据持久化存储
- ✅ 配置备份和恢复
- ✅ 系统状态监控
- ✅ 错误处理机制

## 使用步骤

### 步骤1: 初始化系统
1. 访问设置页面
2. 检查数据库状态
3. 如需要，点击"初始化数据库"

### 步骤2: 配置PX6 API
1. 输入PX6 API密钥
2. 确认API地址正确
3. 点击"测试API连接"验证
4. 保存设置

### 步骤3: 系统配置
1. 根据需要调整系统参数
2. 设置系统公告（可选）
3. 保存所有配置

## API测试功能

系统提供API连接测试功能，用于验证配置的正确性：

- **测试内容**: 验证API密钥和地址的有效性
- **返回信息**: 连接状态、账户余额等
- **错误处理**: 详细的错误信息和解决建议

## 数据存储

### 演示版本
- 使用文件系统存储配置
- 配置文件位置: `data/system_settings.json`
- 自动创建数据目录

### 生产版本
- 使用MySQL数据库存储
- 表名: `system_settings`
- 支持事务和备份

## 迁移说明

### 从.env.local迁移
如果您之前在 `.env.local` 文件中配置了PX6_API_KEY：

1. 复制现有的API密钥值
2. 访问管理员设置页面
3. 在PX6 API配置中粘贴密钥
4. 测试连接并保存
5. 从 `.env.local` 文件中删除相关配置

### 配置文件示例
```bash
# .env.local - 旧的配置方式（已废弃）
# PX6_API_KEY=your_px6_api_key
# PX6_API_URL=https://px6.link/api

# 新的配置方式：请在后台管理页面配置
```

## 故障排除

### 常见问题

#### 1. 无法访问设置页面
- **原因**: 权限不足
- **解决**: 确保使用管理员账户登录

#### 2. API测试失败
- **原因**: 密钥错误或网络问题
- **解决**: 检查密钥格式，确认网络连接

#### 3. 设置保存失败
- **原因**: 数据存储问题
- **解决**: 检查文件权限或数据库连接

#### 4. 数据库初始化失败
- **原因**: 权限或连接问题
- **解决**: 检查数据库配置和权限

### 日志查看
- 服务器日志: 查看控制台输出
- 错误信息: 页面会显示详细错误
- 操作记录: 系统自动记录配置变更

## 安全建议

1. **定期更换密钥**: 建议定期更新API密钥
2. **权限控制**: 仅授权必要人员管理员权限
3. **备份配置**: 定期备份系统配置
4. **监控日志**: 关注异常操作和错误日志

## 技术支持

如果在配置过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 联系技术支持团队
4. 提供详细的错误描述和日志

---

**注意**: 此配置方式提供了更好的安全性和管理便利性，建议所有用户迁移到新的配置方式。
