# 管理员快速设置指南

## 🚀 快速开始

### 1. 创建管理员账户

首次运行系统时，需要创建管理员账户：

```sql
-- 在数据库中手动创建管理员账户
INSERT INTO users (username, email, password_hash, role, created_at) VALUES 
('admin', '<EMAIL>', '$2b$10$hashed_password', 'admin', NOW());
```

或者通过注册页面注册后，手动修改数据库中的role字段为'admin'。

### 2. 访问管理员设置

1. 使用管理员账户登录
2. 访问 `http://localhost:3000/dashboard/settings`
3. 系统会自动显示管理员功能

### 3. 初始化系统

#### 数据库状态检查
- 绿色圆点：数据库已连接
- 红色圆点：数据库未连接
- 黄色圆点：检查中

#### 初始化步骤
1. 点击"检查状态"按钮
2. 如显示未连接，点击"初始化数据库"
3. 等待初始化完成

## ⚙️ PX6 API配置

### 必需配置项

#### PX6 API密钥
```
字段名: px6_api_key
类型: 字符串
必填: 是
最小长度: 10个字符
示例: px6_1234567890abcdef
```

#### PX6 API地址
```
字段名: px6_api_url
类型: URL
必填: 是
默认值: https://api.px6.io
示例: https://api.px6.io
```

### 配置步骤

1. **输入API密钥**
   - 在"PX6 API密钥"字段输入您的密钥
   - 可点击眼睛图标切换显示/隐藏

2. **确认API地址**
   - 默认为 `https://api.px6.io`
   - 如使用自定义地址，请修改此字段

3. **测试连接**
   - 点击"测试API连接"按钮
   - 系统会验证密钥和地址的有效性
   - 成功时显示账户信息

4. **保存设置**
   - 测试成功后点击"保存设置"
   - 系统会持久化存储配置

## 🔧 系统配置

### 用户限制设置

#### 每用户最大代理数量
```
字段名: max_proxies_per_user
类型: 数字
默认值: 10
范围: 1-100
说明: 限制单个用户可创建的代理数量
```

#### 默认代理时长
```
字段名: default_proxy_duration
类型: 数字（天）
默认值: 30
范围: 1-365
说明: 新创建代理的默认有效期
```

### 系统开关

#### 自动续费
```
字段名: enable_auto_renewal
类型: 布尔值
默认值: true
说明: 是否允许代理自动续费
```

#### 维护模式
```
字段名: maintenance_mode
类型: 布尔值
默认值: false
说明: 开启后系统进入维护状态，普通用户无法访问
```

### 系统通知

#### 系统公告
```
字段名: system_notice
类型: 文本
最大长度: 500字符
说明: 在用户控制台顶部显示的公告信息
示例: "系统将于今晚22:00-24:00进行维护，请提前做好准备。"
```

## 📊 管理功能

### 侧边栏菜单

管理员登录后，侧边栏会显示额外的管理功能：

#### 用户功能
- 概览：系统概况和统计
- 我的代理：代理列表管理
- 购买代理：代理购买界面
- 账户充值：余额管理
- 使用统计：使用情况分析
- 使用记录：操作历史
- 个人设置：账户设置

#### 管理员功能
- 管理概览：系统管理总览
- 用户管理：用户账户管理
- 代理管理：全局代理管理
- 系统设置：系统配置管理

### 权限控制

#### 页面访问权限
- 普通用户：只能访问用户功能
- 管理员：可访问所有功能
- 未登录：重定向到登录页

#### API访问权限
- 所有管理员API都需要验证
- 使用JWT token进行身份验证
- 自动检查用户角色权限

## 🔒 安全配置

### 密钥管理

#### 存储方式
- **演示版本**: 文件系统存储 (`data/system_settings.json`)
- **生产版本**: MySQL数据库存储

#### 安全措施
- 密钥输入框支持隐藏显示
- 管理员权限验证
- 操作日志记录
- 定期备份建议

### 访问控制

#### 管理员验证
```typescript
// 简化版验证（演示）
function isAdmin(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization');
  return authHeader && authHeader.includes('Bearer');
}

// 生产版验证
function requireAdmin(request: NextRequest) {
  const token = getTokenFromRequest(request);
  const user = verifyJWT(token);
  if (user.role !== 'admin') {
    throw new Error('需要管理员权限');
  }
  return user;
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 无法访问设置页面
**症状**: 404错误或权限拒绝
**解决方案**:
- 确认使用管理员账户登录
- 检查用户role字段是否为'admin'
- 清除浏览器缓存重试

#### 2. API测试失败
**症状**: 连接测试返回错误
**解决方案**:
- 检查API密钥格式是否正确
- 确认网络连接正常
- 验证API地址是否可访问
- 查看控制台错误日志

#### 3. 设置保存失败
**症状**: 点击保存后显示错误
**解决方案**:
- 检查所有必填字段是否填写
- 确认数据格式正确
- 查看服务器日志
- 检查文件系统权限

#### 4. 数据库初始化失败
**症状**: 初始化按钮无效果
**解决方案**:
- 检查数据库连接配置
- 确认数据库权限
- 查看服务器错误日志
- 手动创建数据目录

### 日志查看

#### 服务器日志
```bash
# 开发环境
npm run dev
# 查看控制台输出

# 生产环境
pm2 logs proxy-system
```

#### 错误调试
```bash
# 检查数据目录
ls -la data/

# 查看设置文件
cat data/system_settings.json

# 检查权限
chmod 755 data/
chmod 644 data/system_settings.json
```

## 📋 检查清单

### 初始设置
- [ ] 创建管理员账户
- [ ] 访问设置页面
- [ ] 初始化数据库/数据目录
- [ ] 配置PX6 API密钥
- [ ] 测试API连接
- [ ] 保存所有设置

### 系统配置
- [ ] 设置用户代理数量限制
- [ ] 配置默认代理时长
- [ ] 启用/禁用自动续费
- [ ] 设置系统公告（可选）
- [ ] 配置维护模式（可选）

### 安全检查
- [ ] 确认密钥安全存储
- [ ] 验证管理员权限正常
- [ ] 测试普通用户访问限制
- [ ] 检查操作日志记录

### 功能验证
- [ ] 测试用户注册登录
- [ ] 验证代理创建功能
- [ ] 检查统计数据显示
- [ ] 确认系统公告显示

---

**提示**: 完成初始设置后，建议定期检查系统状态和更新配置，确保系统稳定运行。
