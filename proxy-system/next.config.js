/** @type {import('next').NextConfig} */
const nextConfig = {
  // Turbopack配置（稳定版本）
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // 实验性功能
  experimental: {
    // 其他实验性功能可以在这里添加
  },

  // 开发模式优化
  ...(process.env.NODE_ENV === 'development' && {
    // 禁用类型检查以加快编译
    typescript: {
      ignoreBuildErrors: true,
    },
    // 禁用ESLint检查以加快编译
    eslint: {
      ignoreDuringBuilds: true,
    },
    // 优化开发服务器
    devIndicators: {
      position: 'bottom-right',
    },
  }),

  // 生产模式优化
  ...(process.env.NODE_ENV === 'production' && {
    // 启用压缩
    compress: true,
    // 启用静态优化
    trailingSlash: false,
    // 优化图片
    images: {
      domains: [],
      formats: ['image/webp', 'image/avif'],
    },
  }),

  // 通用配置
  reactStrictMode: true,
  
  // 允许跨域请求（开发模式）
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },

  // 重定向配置
  async redirects() {
    return [
      // 移除根路径重定向，让首页正常显示
      // {
      //   source: '/',
      //   destination: '/dashboard',
      //   permanent: false,
      // },
    ];
  },

  // Webpack配置优化
  webpack: (config, { dev, isServer }) => {
    // 开发模式优化
    if (dev) {
      // 减少模块解析时间
      config.resolve.symlinks = false;
      
      // 启用缓存
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename],
        },
      };

      // 优化模块解析
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // 通用优化
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 输出配置
  output: 'standalone',
  
  // 页面扩展名
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
};

module.exports = nextConfig;
